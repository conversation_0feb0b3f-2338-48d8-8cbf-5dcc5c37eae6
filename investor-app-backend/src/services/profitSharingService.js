import {
  ProfitSubmission,
  Distribution,
  IndividualPayout,
  Token,
  TokenHolding,
  CapitalPool
} from '../setup/profit_sharing_models.js';
import { User } from '../setup/models.js';
import { logger } from '../utils/logger.js';
import { pool } from '../setup/databases.js';
import { Op } from 'sequelize';
import capitalRecoveryService from './capitalRecoveryService.js';

/**
 * Service for managing profit sharing operations
 */
class ProfitSharingService {
  constructor() {
    this.PLAINS_NORTH_SHARE = 1/3; // Plains North gets 1/3 of net profit
  }

  /**
   * Submit monthly profit and trigger distribution
   */
  async submitMonthlyProfit(profitData, submittedBy) {
    try {
      const { gross_profit, net_profit, submission_period } = profitData;
      
      // Validate submission period format (YYYY-MM)
      if (!/^\d{4}-\d{2}$/.test(submission_period)) {
        throw new Error('Invalid submission period format. Use YYYY-MM');
      }

      // Check if submission already exists for this period
      const existingSubmission = await ProfitSubmission.findOne({
        where: { submission_period }
      });

      if (existingSubmission) {
        throw new Error(`Profit submission already exists for period ${submission_period}`);
      }

      // Calculate Plains North's share
      const plainsNorthShare = parseFloat(net_profit) * this.PLAINS_NORTH_SHARE;

      // Create profit submission record
      const profitSubmission = await ProfitSubmission.create({
        submitted_by: submittedBy,
        gross_profit: parseFloat(gross_profit),
        net_profit: parseFloat(net_profit),
        plains_north_share: plainsNorthShare,
        submission_period,
        status: 'pending',
        metadata: profitData.metadata || {}
      });

      logger.info('Monthly profit submitted', {
        submissionId: profitSubmission.id,
        period: submission_period,
        netProfit: net_profit,
        plainsNorthShare,
        submittedBy
      });

      return {
        success: true,
        submission: profitSubmission,
        plains_north_share: plainsNorthShare
      };
    } catch (error) {
      logger.error('Error submitting monthly profit:', error);
      throw error;
    }
  }

  /**
   * Approve profit submission and trigger distribution
   */
  async approveProfitSubmission(submissionId, approvedBy) {
    try {
      const submission = await ProfitSubmission.findByPk(submissionId);
      if (!submission) {
        throw new Error('Profit submission not found');
      }

      if (submission.status !== 'pending') {
        throw new Error(`Cannot approve submission with status: ${submission.status}`);
      }

      // Update submission status
      await submission.update({
        status: 'approved',
        approved_by: approvedBy,
        approved_at: new Date()
      });

      // Trigger profit distribution
      const distribution = await this.distributeProfit(submission.plains_north_share, approvedBy, submissionId);

      // Link distribution to submission
      await submission.update({
        distribution_id: distribution.id,
        status: 'distributed'
      });

      logger.info('Profit submission approved and distributed', {
        submissionId,
        distributionId: distribution.id,
        approvedBy
      });

      return {
        success: true,
        submission,
        distribution
      };
    } catch (error) {
      logger.error('Error approving profit submission:', error);
      throw error;
    }
  }

  /**
   * Distribute Plains North's profit share based on current stage
   */
  async distributeProfit(amount, initiatedBy, submissionId = null) {
    try {
      const capitalStatus = await capitalRecoveryService.getCapitalStatus();
      const currentStage = capitalStatus.stage;

      let toCapitalPool = 0;
      let toRetainedShare = 0;
      let toTokenisedPool = 0;

      if (currentStage === 'capital_recovery') {
        // During capital recovery: 100% goes to capital pool
        toCapitalPool = parseFloat(amount);
      } else {
        // Post-recovery: 50% to retained share, 50% to tokenised profit pool
        toRetainedShare = parseFloat(amount) / 2;
        toTokenisedPool = parseFloat(amount) - toRetainedShare;
      }

      // Create distribution record
      const distribution = await Distribution.create({
        distribution_type: 'monthly_profit',
        total_amount: parseFloat(amount),
        to_capital_pool: toCapitalPool,
        to_retained_share: toRetainedShare,
        to_tokenised_pool: toTokenisedPool,
        stage: currentStage,
        initiated_by: initiatedBy,
        status: 'processing',
        metadata: { submission_id: submissionId }
      });

      // Execute the distribution
      await this.executeDistribution(distribution);

      logger.info('Profit distributed', {
        distributionId: distribution.id,
        amount,
        stage: currentStage,
        toCapitalPool,
        toRetainedShare,
        toTokenisedPool
      });

      return distribution;
    } catch (error) {
      logger.error('Error distributing profit:', error);
      throw error;
    }
  }

  /**
   * Execute the actual distribution
   */
  async executeDistribution(distribution) {
    try {
      // Update capital pool if applicable
      if (distribution.to_capital_pool > 0) {
        await capitalRecoveryService.recordPayment(
          distribution.to_capital_pool,
          distribution.blockchain_tx_hash,
          distribution.initiated_by
        );
      }

      // Distribute to token holders if applicable
      if (distribution.to_tokenised_pool > 0) {
        await this.distributeToTokenHolders(distribution);
      }

      // Update distribution status
      await distribution.update({
        status: 'completed',
        metadata: {
          ...distribution.metadata,
          completed_at: new Date().toISOString()
        }
      });

      return distribution;
    } catch (error) {
      logger.error('Error executing distribution:', error);
      
      // Update distribution status to failed
      await distribution.update({
        status: 'failed',
        metadata: {
          ...distribution.metadata,
          error: error.message,
          failed_at: new Date().toISOString()
        }
      });
      
      throw error;
    }
  }

  /**
   * Distribute funds to token holders pro-rata
   */
  async distributeToTokenHolders(distribution) {
    try {
      // Get active profit sharing token
      const token = await Token.findOne({
        where: { 
          token_type: 'profit_sharing',
          is_active: true 
        }
      });

      if (!token) {
        throw new Error('No active profit sharing token found');
      }

      // Get all token holders with non-zero balances
      const tokenHolders = await TokenHolding.findAll({
        where: { 
          token_id: token.id,
          balance: { [Op.gt]: 0 }
        },
        include: [{
          model: User,
          as: 'investor',
          attributes: ['id', 'company_name', 'email', 'kyc_status']
        }]
      });

      if (tokenHolders.length === 0) {
        logger.warn('No token holders found for distribution');
        return;
      }

      const totalSupply = parseFloat(token.total_supply);
      const distributionAmount = parseFloat(distribution.to_tokenised_pool);

      // Create individual payouts
      const payouts = [];
      for (const holder of tokenHolders) {
        // Only distribute to KYC verified investors
        if (holder.investor.kyc_status !== 'verified') {
          logger.warn(`Skipping distribution to unverified investor: ${holder.investor.id}`);
          continue;
        }

        const holderBalance = parseFloat(holder.balance);
        const payoutPercentage = holderBalance / totalSupply;
        const payoutAmount = distributionAmount * payoutPercentage;

        if (payoutAmount > 0) {
          const payout = await IndividualPayout.create({
            distribution_id: distribution.id,
            investor_id: holder.investor_id,
            token_id: token.id,
            amount: payoutAmount,
            token_balance_at_time: holderBalance,
            total_supply_at_time: totalSupply,
            payout_percentage: payoutPercentage * 100,
            status: 'pending'
          });

          payouts.push(payout);
        }
      }

      logger.info('Individual payouts created', {
        distributionId: distribution.id,
        payoutCount: payouts.length,
        totalAmount: distributionAmount
      });

      return payouts;
    } catch (error) {
      logger.error('Error distributing to token holders:', error);
      throw error;
    }
  }

  /**
   * Get distribution history
   */
  async getDistributionHistory(limit = 50, offset = 0) {
    try {
      const distributions = await Distribution.findAll({
        limit,
        offset,
        order: [['distribution_date', 'DESC']],
        include: [
          {
            model: User,
            as: 'initiatedBy',
            attributes: ['id', 'company_name', 'contact_name']
          },
          {
            model: IndividualPayout,
            as: 'payouts',
            include: [{
              model: User,
              as: 'investor',
              attributes: ['id', 'company_name']
            }]
          }
        ]
      });

      return distributions;
    } catch (error) {
      logger.error('Error getting distribution history:', error);
      throw error;
    }
  }

  /**
   * Get investor payout history
   */
  async getInvestorPayoutHistory(investorId, limit = 50, offset = 0) {
    try {
      const payouts = await IndividualPayout.findAll({
        where: { investor_id: investorId },
        limit,
        offset,
        order: [['created_at', 'DESC']],
        include: [
          {
            model: Distribution,
            as: 'distribution',
            attributes: ['id', 'distribution_type', 'distribution_date', 'stage']
          },
          {
            model: Token,
            as: 'token',
            attributes: ['id', 'token_name', 'token_symbol']
          }
        ]
      });

      return payouts;
    } catch (error) {
      logger.error('Error getting investor payout history:', error);
      throw error;
    }
  }

  /**
   * Get profit sharing statistics
   */
  async getProfitSharingStatistics() {
    try {
      const stats = await pool.query(`
        SELECT 
          COUNT(DISTINCT ps.id) as total_submissions,
          COUNT(DISTINCT d.id) as total_distributions,
          SUM(ps.net_profit) as total_net_profit,
          SUM(ps.plains_north_share) as total_plains_north_share,
          SUM(d.to_capital_pool) as total_to_capital_pool,
          SUM(d.to_retained_share) as total_to_retained_share,
          SUM(d.to_tokenised_pool) as total_to_tokenised_pool,
          COUNT(DISTINCT ip.investor_id) as unique_investors_paid,
          SUM(ip.amount) as total_investor_payouts
        FROM profit_submissions ps
        LEFT JOIN distributions d ON ps.distribution_id = d.id
        LEFT JOIN individual_payouts ip ON d.id = ip.distribution_id
      `);

      return stats.rows[0];
    } catch (error) {
      logger.error('Error getting profit sharing statistics:', error);
      throw error;
    }
  }
}

export default new ProfitSharingService();
