import { CapitalPool, BlockchainEvent } from '../setup/profit_sharing_models.js';
import { logger } from '../utils/logger.js';
import { pool } from '../setup/databases.js';

/**
 * Service for managing capital recovery tracking and stage transitions
 */
class CapitalRecoveryService {
  constructor() {
    this.TARGET_AMOUNT = 150000.00; // CAD 150k
  }

  /**
   * Get current capital recovery status
   */
  async getCapitalStatus() {
    try {
      const capitalPool = await CapitalPool.findOne({
        order: [['last_updated', 'DESC']]
      });

      if (!capitalPool) {
        // Create initial record if none exists
        const newCapitalPool = await CapitalPool.create({
          target_amount: this.TARGET_AMOUNT,
          repaid_amount: 0.00,
          stage: 'capital_recovery'
        });
        return this.formatCapitalStatus(newCapitalPool);
      }

      return this.formatCapitalStatus(capitalPool);
    } catch (error) {
      logger.error('Error getting capital status:', error);
      throw error;
    }
  }

  /**
   * Record a payment towards capital recovery
   */
  async recordPayment(amount, txHash = null, updatedBy = null) {
    try {
      const currentStatus = await this.getCapitalStatus();
      const newRepaidAmount = parseFloat(currentStatus.repaid_amount) + parseFloat(amount);
      
      // Determine new stage
      const newStage = newRepaidAmount >= this.TARGET_AMOUNT ? 'post_recovery' : 'capital_recovery';
      const stageChanged = newStage !== currentStatus.stage;

      // Update capital pool
      const updatedCapitalPool = await CapitalPool.update({
        repaid_amount: newRepaidAmount,
        stage: newStage,
        last_updated: new Date(),
        updated_by: updatedBy,
        blockchain_tx_hash: txHash
      }, {
        where: { id: currentStatus.id },
        returning: true
      });

      const result = updatedCapitalPool[1][0];

      // Log stage transition if it occurred
      if (stageChanged) {
        logger.info(`Capital recovery stage transition: ${currentStatus.stage} -> ${newStage}`, {
          previousAmount: currentStatus.repaid_amount,
          newAmount: newRepaidAmount,
          targetAmount: this.TARGET_AMOUNT,
          txHash
        });

        // Record blockchain event for stage transition
        if (txHash) {
          await this.recordStageTransitionEvent(currentStatus.stage, newStage, txHash, amount);
        }
      }

      return {
        success: true,
        stageChanged,
        previousStage: currentStatus.stage,
        newStage,
        capitalStatus: this.formatCapitalStatus(result)
      };
    } catch (error) {
      logger.error('Error recording payment:', error);
      throw error;
    }
  }

  /**
   * Record stage transition event
   */
  async recordStageTransitionEvent(previousStage, newStage, txHash, amount) {
    try {
      await BlockchainEvent.create({
        event_type: 'StageTransition',
        contract_address: process.env.CAPITAL_RECOVERY_CONTRACT_ADDRESS || '',
        transaction_hash: txHash,
        block_number: 0, // Will be updated by event listener
        block_timestamp: new Date(),
        event_data: {
          previousStage,
          newStage,
          amount,
          timestamp: new Date().toISOString()
        },
        processed: true,
        processed_at: new Date()
      });
    } catch (error) {
      logger.error('Error recording stage transition event:', error);
      // Don't throw - this is not critical for the main operation
    }
  }

  /**
   * Get capital recovery history
   */
  async getCapitalHistory(limit = 50, offset = 0) {
    try {
      const query = `
        SELECT 
          cp.*,
          ii.company_name as updated_by_name,
          ii.contact_name as updated_by_contact
        FROM capital_pool cp
        LEFT JOIN institutional_investors ii ON cp.updated_by = ii.id
        ORDER BY cp.last_updated DESC
        LIMIT $1 OFFSET $2
      `;

      const result = await pool.query(query, [limit, offset]);
      return result.rows.map(row => this.formatCapitalStatus(row));
    } catch (error) {
      logger.error('Error getting capital history:', error);
      throw error;
    }
  }

  /**
   * Get recovery statistics
   */
  async getRecoveryStatistics() {
    try {
      const currentStatus = await this.getCapitalStatus();
      
      // Get payment history
      const paymentHistory = await this.getCapitalHistory(100);
      
      // Calculate statistics
      const totalPayments = paymentHistory.length;
      const averagePayment = totalPayments > 0 
        ? paymentHistory.reduce((sum, payment) => sum + parseFloat(payment.repaid_amount || 0), 0) / totalPayments
        : 0;

      // Get monthly breakdown
      const monthlyBreakdown = await this.getMonthlyBreakdown();

      return {
        current_status: currentStatus,
        total_payments: totalPayments,
        average_payment: averagePayment,
        monthly_breakdown: monthlyBreakdown,
        projected_completion: this.calculateProjectedCompletion(currentStatus, monthlyBreakdown)
      };
    } catch (error) {
      logger.error('Error getting recovery statistics:', error);
      throw error;
    }
  }

  /**
   * Get monthly payment breakdown
   */
  async getMonthlyBreakdown() {
    try {
      const query = `
        SELECT 
          DATE_TRUNC('month', last_updated) as month,
          COUNT(*) as payment_count,
          SUM(repaid_amount) as total_amount
        FROM capital_pool
        WHERE last_updated >= NOW() - INTERVAL '12 months'
        GROUP BY DATE_TRUNC('month', last_updated)
        ORDER BY month DESC
      `;

      const result = await pool.query(query);
      return result.rows;
    } catch (error) {
      logger.error('Error getting monthly breakdown:', error);
      throw error;
    }
  }

  /**
   * Calculate projected completion date
   */
  calculateProjectedCompletion(currentStatus, monthlyBreakdown) {
    try {
      if (currentStatus.stage === 'post_recovery') {
        return {
          status: 'completed',
          completion_date: null
        };
      }

      const remaining = parseFloat(currentStatus.remaining_amount);
      
      if (monthlyBreakdown.length === 0) {
        return {
          status: 'insufficient_data',
          completion_date: null
        };
      }

      // Calculate average monthly payment from last 6 months
      const recentPayments = monthlyBreakdown.slice(0, 6);
      const avgMonthlyPayment = recentPayments.reduce((sum, month) => 
        sum + parseFloat(month.total_amount || 0), 0) / recentPayments.length;

      if (avgMonthlyPayment <= 0) {
        return {
          status: 'no_recent_payments',
          completion_date: null
        };
      }

      const monthsToCompletion = Math.ceil(remaining / avgMonthlyPayment);
      const projectedDate = new Date();
      projectedDate.setMonth(projectedDate.getMonth() + monthsToCompletion);

      return {
        status: 'projected',
        completion_date: projectedDate,
        months_remaining: monthsToCompletion,
        avg_monthly_payment: avgMonthlyPayment
      };
    } catch (error) {
      logger.error('Error calculating projected completion:', error);
      return {
        status: 'error',
        completion_date: null
      };
    }
  }

  /**
   * Format capital status for API response
   */
  formatCapitalStatus(capitalPool) {
    if (!capitalPool) return null;

    const targetAmount = parseFloat(capitalPool.target_amount || this.TARGET_AMOUNT);
    const repaidAmount = parseFloat(capitalPool.repaid_amount || 0);
    const remainingAmount = targetAmount - repaidAmount;
    const recoveryPercentage = targetAmount > 0 ? Math.min(100, (repaidAmount / targetAmount) * 100) : 0;

    return {
      id: capitalPool.id,
      target_amount: targetAmount,
      repaid_amount: repaidAmount,
      remaining_amount: Math.max(0, remainingAmount),
      recovery_percentage: Math.round(recoveryPercentage * 100) / 100,
      stage: capitalPool.stage,
      last_updated: capitalPool.last_updated,
      updated_by: capitalPool.updated_by,
      blockchain_tx_hash: capitalPool.blockchain_tx_hash,
      metadata: capitalPool.metadata || {}
    };
  }

  /**
   * Reset capital recovery (admin only)
   */
  async resetCapitalRecovery(updatedBy = null) {
    try {
      const result = await CapitalPool.update({
        repaid_amount: 0.00,
        stage: 'capital_recovery',
        last_updated: new Date(),
        updated_by: updatedBy,
        blockchain_tx_hash: null
      }, {
        where: {},
        returning: true
      });

      logger.info('Capital recovery reset', { updatedBy });
      return this.formatCapitalStatus(result[1][0]);
    } catch (error) {
      logger.error('Error resetting capital recovery:', error);
      throw error;
    }
  }
}

export default new CapitalRecoveryService();
