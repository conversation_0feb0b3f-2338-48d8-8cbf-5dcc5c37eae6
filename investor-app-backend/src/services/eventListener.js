import { ethers } from 'ethers';
import { BlockchainEvent } from '../setup/profit_sharing_models.js';
import { logger } from '../utils/logger.js';
import blockchainService from './blockchainService.js';

/**
 * Service for listening to blockchain events and processing them
 */
class EventListenerService {
  constructor() {
    this.provider = null;
    this.contracts = {};
    this.isListening = false;
    this.listeners = new Map();
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 10;
    this.reconnectDelay = 5000;
    this.lastProcessedBlock = 0;
  }

  /**
   * Initialize event listener service
   */
  async initialize() {
    try {
      // Initialize provider
      const rpcUrl = process.env.ETH_RPC_URL || 'http://localhost:8545';
      const wsUrl = process.env.ETH_WS_URL;

      if (wsUrl && (wsUrl.startsWith('ws://') || wsUrl.startsWith('wss://'))) {
        this.provider = new ethers.WebSocketProvider(wsUrl);
      } else {
        this.provider = new ethers.JsonRpcProvider(rpcUrl);
      }

      // Test connection
      await this.provider.getNetwork();
      
      // Initialize contracts
      await this.initializeContracts();
      
      // Get last processed block
      await this.getLastProcessedBlock();
      
      logger.info('Event listener service initialized successfully');
    } catch (error) {
      logger.error('Error initializing event listener service:', error);
      throw error;
    }
  }

  /**
   * Initialize smart contracts for event listening
   */
  async initializeContracts() {
    try {
      const contractConfigs = [
        {
          name: 'profitSplitter',
          address: process.env.PROFIT_SPLITTER_CONTRACT_ADDRESS,
          abi: [
            "event ProfitReceived(uint256 amount, address indexed sender)",
            "event ProfitDistributed(uint256 distributionId, uint256 totalAmount, uint256 toCapitalPool, uint256 toRetainedShare, uint256 toTokenisedPool, uint8 stage)"
          ]
        },
        {
          name: 'capitalRecovery',
          address: process.env.CAPITAL_RECOVERY_CONTRACT_ADDRESS,
          abi: [
            "event PaymentRecorded(uint256 amount, uint256 cumulativeTotal, address indexed recorder)",
            "event StageTransition(uint8 previousStage, uint8 newStage, uint256 timestamp)"
          ]
        },
        {
          name: 'tokenDistributor',
          address: process.env.TOKEN_DISTRIBUTOR_CONTRACT_ADDRESS,
          abi: [
            "event FundsReceived(uint256 amount, address indexed sender)",
            "event DistributionExecuted(uint256 distributionId, uint256 totalAmount, uint256 timestamp)",
            "event PayoutSent(address indexed investor, uint256 amount, uint256 distributionId)"
          ]
        },
        {
          name: 'profitToken',
          address: process.env.PROFIT_TOKEN_CONTRACT_ADDRESS,
          abi: [
            "event Transfer(address indexed from, address indexed to, uint256 value)",
            "event KYCStatusUpdated(address indexed investor, bool verified)"
          ]
        }
      ];

      for (const config of contractConfigs) {
        if (config.address) {
          this.contracts[config.name] = new ethers.Contract(
            config.address,
            config.abi,
            this.provider
          );
          logger.info(`Contract ${config.name} initialized at ${config.address}`);
        }
      }
    } catch (error) {
      logger.error('Error initializing contracts for event listening:', error);
      throw error;
    }
  }

  /**
   * Start listening to blockchain events
   */
  async startListening() {
    try {
      if (this.isListening) {
        logger.warn('Event listener is already running');
        return;
      }

      if (!this.provider) {
        await this.initialize();
      }

      // Set up event listeners for each contract
      await this.setupEventListeners();
      
      // Process any missed events
      await this.processMissedEvents();
      
      this.isListening = true;
      this.reconnectAttempts = 0;
      
      logger.info('Event listener started successfully');
    } catch (error) {
      logger.error('Error starting event listener:', error);
      await this.handleReconnection();
    }
  }

  /**
   * Stop listening to blockchain events
   */
  async stopListening() {
    try {
      this.isListening = false;
      
      // Remove all event listeners
      for (const [eventKey, listener] of this.listeners) {
        try {
          const [contractName, eventName] = eventKey.split(':');
          if (this.contracts[contractName]) {
            this.contracts[contractName].off(eventName, listener);
          }
        } catch (error) {
          logger.error(`Error removing listener for ${eventKey}:`, error);
        }
      }
      
      this.listeners.clear();
      
      // Close provider connection if it's a WebSocket
      if (this.provider && this.provider.websocket) {
        this.provider.websocket.close();
      }
      
      logger.info('Event listener stopped');
    } catch (error) {
      logger.error('Error stopping event listener:', error);
    }
  }

  /**
   * Setup event listeners for all contracts
   */
  async setupEventListeners() {
    try {
      // Profit Splitter events
      if (this.contracts.profitSplitter) {
        this.addEventListeners('profitSplitter', [
          'ProfitReceived',
          'ProfitDistributed'
        ]);
      }

      // Capital Recovery events
      if (this.contracts.capitalRecovery) {
        this.addEventListeners('capitalRecovery', [
          'PaymentRecorded',
          'StageTransition'
        ]);
      }

      // Token Distributor events
      if (this.contracts.tokenDistributor) {
        this.addEventListeners('tokenDistributor', [
          'FundsReceived',
          'DistributionExecuted',
          'PayoutSent'
        ]);
      }

      // Profit Token events
      if (this.contracts.profitToken) {
        this.addEventListeners('profitToken', [
          'Transfer',
          'KYCStatusUpdated'
        ]);
      }

      logger.info('Event listeners setup completed');
    } catch (error) {
      logger.error('Error setting up event listeners:', error);
      throw error;
    }
  }

  /**
   * Add event listeners for a specific contract
   */
  addEventListeners(contractName, eventNames) {
    const contract = this.contracts[contractName];
    if (!contract) return;

    for (const eventName of eventNames) {
      const listener = (...args) => {
        this.handleEvent(contractName, eventName, args);
      };

      contract.on(eventName, listener);
      this.listeners.set(`${contractName}:${eventName}`, listener);
      
      logger.debug(`Event listener added: ${contractName}.${eventName}`);
    }
  }

  /**
   * Handle incoming blockchain event
   */
  async handleEvent(contractName, eventName, args) {
    try {
      // The last argument is typically the event object with transaction details
      const event = args[args.length - 1];
      
      if (!event || !event.transactionHash) {
        logger.warn('Invalid event received', { contractName, eventName });
        return;
      }

      // Extract event data
      const eventData = this.extractEventData(eventName, args);
      
      // Store event in database
      await this.storeBlockchainEvent({
        event_type: eventName,
        contract_address: event.address,
        transaction_hash: event.transactionHash,
        block_number: event.blockNumber,
        block_timestamp: new Date(), // Will be updated with actual block timestamp
        event_data: eventData
      });

      // Process the event
      await this.processEvent({
        event_type: eventName,
        event_data: eventData,
        transaction_hash: event.transactionHash
      });

      // Update last processed block
      if (event.blockNumber > this.lastProcessedBlock) {
        this.lastProcessedBlock = event.blockNumber;
      }

      logger.info('Event processed successfully', {
        contract: contractName,
        event: eventName,
        txHash: event.transactionHash,
        blockNumber: event.blockNumber
      });
    } catch (error) {
      logger.error('Error handling event:', error, {
        contractName,
        eventName,
        args
      });
    }
  }

  /**
   * Extract event data from arguments
   */
  extractEventData(eventName, args) {
    try {
      // Remove the event object (last argument)
      const eventArgs = args.slice(0, -1);
      
      switch (eventName) {
        case 'ProfitReceived':
          return {
            amount: eventArgs[0].toString(),
            sender: eventArgs[1]
          };
        case 'ProfitDistributed':
          return {
            distributionId: eventArgs[0].toString(),
            totalAmount: eventArgs[1].toString(),
            toCapitalPool: eventArgs[2].toString(),
            toRetainedShare: eventArgs[3].toString(),
            toTokenisedPool: eventArgs[4].toString(),
            stage: eventArgs[5]
          };
        case 'PaymentRecorded':
          return {
            amount: eventArgs[0].toString(),
            cumulativeTotal: eventArgs[1].toString(),
            recorder: eventArgs[2]
          };
        case 'StageTransition':
          return {
            previousStage: eventArgs[0],
            newStage: eventArgs[1],
            timestamp: eventArgs[2].toString()
          };
        case 'Transfer':
          return {
            from: eventArgs[0],
            to: eventArgs[1],
            value: eventArgs[2].toString()
          };
        case 'KYCStatusUpdated':
          return {
            investor: eventArgs[0],
            verified: eventArgs[1]
          };
        default:
          return { args: eventArgs.map(arg => arg.toString()) };
      }
    } catch (error) {
      logger.error('Error extracting event data:', error);
      return { error: 'Failed to extract event data' };
    }
  }

  /**
   * Store blockchain event in database
   */
  async storeBlockchainEvent(eventData) {
    try {
      // Check if event already exists
      const existingEvent = await BlockchainEvent.findOne({
        where: {
          transaction_hash: eventData.transaction_hash,
          event_type: eventData.event_type
        }
      });

      if (existingEvent) {
        logger.debug('Event already exists in database', {
          txHash: eventData.transaction_hash,
          eventType: eventData.event_type
        });
        return existingEvent;
      }

      // Get actual block timestamp
      try {
        const block = await this.provider.getBlock(eventData.block_number);
        eventData.block_timestamp = new Date(block.timestamp * 1000);
      } catch (error) {
        logger.warn('Could not get block timestamp:', error);
      }

      const event = await BlockchainEvent.create(eventData);
      return event;
    } catch (error) {
      logger.error('Error storing blockchain event:', error);
      throw error;
    }
  }

  /**
   * Process blockchain event
   */
  async processEvent(eventData) {
    try {
      await blockchainService.processBlockchainEvent(eventData);
    } catch (error) {
      logger.error('Error processing event:', error);
      throw error;
    }
  }

  /**
   * Process missed events since last processed block
   */
  async processMissedEvents() {
    try {
      if (this.lastProcessedBlock === 0) {
        logger.info('No previous block recorded, skipping missed events processing');
        return;
      }

      const currentBlock = await this.provider.getBlockNumber();
      const blocksToProcess = currentBlock - this.lastProcessedBlock;

      if (blocksToProcess <= 0) {
        logger.info('No missed blocks to process');
        return;
      }

      logger.info(`Processing ${blocksToProcess} missed blocks`, {
        fromBlock: this.lastProcessedBlock + 1,
        toBlock: currentBlock
      });

      // Process events for each contract
      for (const [contractName, contract] of Object.entries(this.contracts)) {
        try {
          const filter = {
            address: contract.address,
            fromBlock: this.lastProcessedBlock + 1,
            toBlock: currentBlock
          };

          const logs = await this.provider.getLogs(filter);
          
          for (const log of logs) {
            try {
              const parsedLog = contract.interface.parseLog(log);
              if (parsedLog) {
                await this.handleEvent(contractName, parsedLog.name, [
                  ...parsedLog.args,
                  {
                    address: log.address,
                    transactionHash: log.transactionHash,
                    blockNumber: log.blockNumber
                  }
                ]);
              }
            } catch (error) {
              logger.error('Error processing missed event:', error);
            }
          }
        } catch (error) {
          logger.error(`Error processing missed events for ${contractName}:`, error);
        }
      }

      this.lastProcessedBlock = currentBlock;
    } catch (error) {
      logger.error('Error processing missed events:', error);
    }
  }

  /**
   * Get last processed block from database or environment
   */
  async getLastProcessedBlock() {
    try {
      // Try to get from environment first
      const envBlock = process.env.LAST_PROCESSED_BLOCK;
      if (envBlock) {
        this.lastProcessedBlock = parseInt(envBlock);
        return;
      }

      // Get from database
      const lastEvent = await BlockchainEvent.findOne({
        order: [['block_number', 'DESC']]
      });

      if (lastEvent) {
        this.lastProcessedBlock = lastEvent.block_number;
      } else {
        // Start from current block if no history
        this.lastProcessedBlock = await this.provider.getBlockNumber();
      }

      logger.info(`Last processed block: ${this.lastProcessedBlock}`);
    } catch (error) {
      logger.error('Error getting last processed block:', error);
      this.lastProcessedBlock = 0;
    }
  }

  /**
   * Handle reconnection logic
   */
  async handleReconnection() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      logger.error('Max reconnection attempts reached, stopping event listener');
      return;
    }

    this.reconnectAttempts++;
    logger.info(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

    setTimeout(async () => {
      try {
        await this.stopListening();
        await this.initialize();
        await this.startListening();
      } catch (error) {
        logger.error('Reconnection failed:', error);
        await this.handleReconnection();
      }
    }, this.reconnectDelay * this.reconnectAttempts);
  }

  /**
   * Get service health status
   */
  getHealthStatus() {
    return {
      initialized: !!this.provider,
      listening: this.isListening,
      last_processed_block: this.lastProcessedBlock,
      active_listeners: this.listeners.size,
      reconnect_attempts: this.reconnectAttempts,
      contracts_loaded: Object.keys(this.contracts).length
    };
  }

  /**
   * Restart the event listener service
   */
  async restart() {
    try {
      logger.info('Restarting event listener service');
      await this.stopListening();
      await this.initialize();
      await this.startListening();
      logger.info('Event listener service restarted successfully');
    } catch (error) {
      logger.error('Error restarting event listener service:', error);
      throw error;
    }
  }
}

export default new EventListenerService();
