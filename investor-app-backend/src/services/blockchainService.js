import { ethers } from 'ethers';
import { BlockchainEvent, Token, TokenHolding } from '../setup/profit_sharing_models.js';
import { User } from '../setup/models.js';
import { logger } from '../utils/logger.js';
import capitalRecoveryService from './capitalRecoveryService.js';

/**
 * Service for blockchain integration and smart contract interactions
 */
class BlockchainService {
  constructor() {
    this.provider = null;
    this.contracts = {};
    this.isInitialized = false;
  }

  /**
   * Initialize blockchain connection and contracts
   */
  async initialize() {
    try {
      // Initialize provider
      const rpcUrl = process.env.ETH_RPC_URL || 'http://localhost:8545';
      this.provider = new ethers.JsonRpcProvider(rpcUrl);

      // Test connection
      await this.provider.getNetwork();
      
      // Initialize contracts
      await this.initializeContracts();
      
      this.isInitialized = true;
      logger.info('Blockchain service initialized successfully');
    } catch (error) {
      logger.error('Error initializing blockchain service:', error);
      throw error;
    }
  }

  /**
   * Initialize smart contracts
   */
  async initializeContracts() {
    try {
      // Contract addresses from environment
      const profitSplitterAddress = process.env.PROFIT_SPLITTER_CONTRACT_ADDRESS;
      const capitalRecoveryAddress = process.env.CAPITAL_RECOVERY_CONTRACT_ADDRESS;
      const tokenDistributorAddress = process.env.TOKEN_DISTRIBUTOR_CONTRACT_ADDRESS;
      const profitTokenAddress = process.env.PROFIT_TOKEN_CONTRACT_ADDRESS;

      // Contract ABIs (simplified for key functions)
      const profitSplitterABI = [
        "event ProfitReceived(uint256 amount, address indexed sender)",
        "event ProfitDistributed(uint256 distributionId, uint256 totalAmount, uint256 toCapitalPool, uint256 toRetainedShare, uint256 toTokenisedPool, uint8 stage)",
        "function distributeProfit() external",
        "function getLatestDistribution() external view returns (tuple(uint256 totalAmount, uint256 toCapitalPool, uint256 toRetainedShare, uint256 toTokenisedPool, uint256 timestamp, uint8 stage))"
      ];

      const capitalRecoveryABI = [
        "event PaymentRecorded(uint256 amount, uint256 cumulativeTotal, address indexed recorder)",
        "event StageTransition(uint8 previousStage, uint8 newStage, uint256 timestamp)",
        "function recordPayment(uint256 amount) external",
        "function getStage() external view returns (uint8)",
        "function remaining() external view returns (uint256)"
      ];

      const tokenDistributorABI = [
        "event FundsReceived(uint256 amount, address indexed sender)",
        "event DistributionExecuted(uint256 distributionId, uint256 totalAmount, uint256 timestamp)",
        "event PayoutSent(address indexed investor, uint256 amount, uint256 distributionId)",
        "function distribute() external",
        "function distributeToInvestor(address investor) external"
      ];

      const profitTokenABI = [
        "event Transfer(address indexed from, address indexed to, uint256 value)",
        "event KYCStatusUpdated(address indexed investor, bool verified)",
        "function balanceOf(address account) external view returns (uint256)",
        "function totalSupply() external view returns (uint256)",
        "function updateKYCStatus(address investor, bool verified) external"
      ];

      // Initialize contracts if addresses are provided
      if (profitSplitterAddress) {
        this.contracts.profitSplitter = new ethers.Contract(profitSplitterAddress, profitSplitterABI, this.provider);
      }

      if (capitalRecoveryAddress) {
        this.contracts.capitalRecovery = new ethers.Contract(capitalRecoveryAddress, capitalRecoveryABI, this.provider);
      }

      if (tokenDistributorAddress) {
        this.contracts.tokenDistributor = new ethers.Contract(tokenDistributorAddress, tokenDistributorABI, this.provider);
      }

      if (profitTokenAddress) {
        this.contracts.profitToken = new ethers.Contract(profitTokenAddress, profitTokenABI, this.provider);
      }

      logger.info('Smart contracts initialized', {
        profitSplitter: !!profitSplitterAddress,
        capitalRecovery: !!capitalRecoveryAddress,
        tokenDistributor: !!tokenDistributorAddress,
        profitToken: !!profitTokenAddress
      });
    } catch (error) {
      logger.error('Error initializing contracts:', error);
      throw error;
    }
  }

  /**
   * Get token balance for an investor
   */
  async getTokenBalance(investorAddress, tokenAddress = null) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const contractAddress = tokenAddress || process.env.PROFIT_TOKEN_CONTRACT_ADDRESS;
      if (!contractAddress) {
        throw new Error('Token contract address not configured');
      }

      const tokenContract = this.contracts.profitToken || 
        new ethers.Contract(contractAddress, [
          "function balanceOf(address account) external view returns (uint256)"
        ], this.provider);

      const balance = await tokenContract.balanceOf(investorAddress);
      return balance.toString();
    } catch (error) {
      logger.error('Error getting token balance:', error);
      throw error;
    }
  }

  /**
   * Update KYC status on blockchain
   */
  async updateKYCStatus(investorAddress, verified, privateKey) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      if (!this.contracts.profitToken) {
        throw new Error('Profit token contract not initialized');
      }

      const wallet = new ethers.Wallet(privateKey, this.provider);
      const contract = this.contracts.profitToken.connect(wallet);

      const tx = await contract.updateKYCStatus(investorAddress, verified);
      await tx.wait();

      logger.info('KYC status updated on blockchain', {
        investor: investorAddress,
        verified,
        txHash: tx.hash
      });

      return tx.hash;
    } catch (error) {
      logger.error('Error updating KYC status on blockchain:', error);
      throw error;
    }
  }

  /**
   * Trigger profit distribution on blockchain
   */
  async triggerProfitDistribution(privateKey) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      if (!this.contracts.profitSplitter) {
        throw new Error('Profit splitter contract not initialized');
      }

      const wallet = new ethers.Wallet(privateKey, this.provider);
      const contract = this.contracts.profitSplitter.connect(wallet);

      const tx = await contract.distributeProfit();
      await tx.wait();

      logger.info('Profit distribution triggered on blockchain', {
        txHash: tx.hash
      });

      return tx.hash;
    } catch (error) {
      logger.error('Error triggering profit distribution:', error);
      throw error;
    }
  }

  /**
   * Get latest distribution from blockchain
   */
  async getLatestDistribution() {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      if (!this.contracts.profitSplitter) {
        throw new Error('Profit splitter contract not initialized');
      }

      const distribution = await this.contracts.profitSplitter.getLatestDistribution();
      
      return {
        totalAmount: ethers.formatEther(distribution.totalAmount),
        toCapitalPool: ethers.formatEther(distribution.toCapitalPool),
        toRetainedShare: ethers.formatEther(distribution.toRetainedShare),
        toTokenisedPool: ethers.formatEther(distribution.toTokenisedPool),
        timestamp: new Date(Number(distribution.timestamp) * 1000),
        stage: distribution.stage === 0 ? 'capital_recovery' : 'post_recovery'
      };
    } catch (error) {
      logger.error('Error getting latest distribution:', error);
      throw error;
    }
  }

  /**
   * Sync token holdings from blockchain
   */
  async syncTokenHoldings() {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      // Get all users with wallet addresses
      const users = await User.findAll({
        where: {
          // Assuming wallet_address field exists or can be derived
          // This might need adjustment based on your user model
        }
      });

      // Get active profit sharing token
      const token = await Token.findOne({
        where: { 
          token_type: 'profit_sharing',
          is_active: true 
        }
      });

      if (!token) {
        logger.warn('No active profit sharing token found for sync');
        return;
      }

      let syncedCount = 0;
      for (const user of users) {
        try {
          // This assumes users have a wallet_address field
          // You may need to adjust based on your user model
          const walletAddress = user.wallet_address;
          if (!walletAddress) continue;

          const balance = await this.getTokenBalance(walletAddress, token.contract_address);
          
          // Update or create token holding record
          await TokenHolding.upsert({
            token_id: token.id,
            investor_id: user.id,
            balance: balance,
            last_updated: new Date()
          });

          syncedCount++;
        } catch (error) {
          logger.error(`Error syncing token holdings for user ${user.id}:`, error);
        }
      }

      logger.info('Token holdings synced', {
        tokenId: token.id,
        syncedCount,
        totalUsers: users.length
      });

      return { syncedCount, totalUsers: users.length };
    } catch (error) {
      logger.error('Error syncing token holdings:', error);
      throw error;
    }
  }

  /**
   * Process blockchain event
   */
  async processBlockchainEvent(eventData) {
    try {
      const { event_type, event_data, transaction_hash } = eventData;

      switch (event_type) {
        case 'StageTransition':
          await this.handleStageTransition(event_data, transaction_hash);
          break;
        case 'ProfitDistributed':
          await this.handleProfitDistributed(event_data, transaction_hash);
          break;
        case 'PaymentRecorded':
          await this.handlePaymentRecorded(event_data, transaction_hash);
          break;
        case 'Transfer':
          await this.handleTokenTransfer(event_data, transaction_hash);
          break;
        default:
          logger.warn(`Unknown event type: ${event_type}`);
      }

      // Mark event as processed
      await BlockchainEvent.update(
        { processed: true, processed_at: new Date() },
        { where: { transaction_hash, event_type } }
      );

    } catch (error) {
      logger.error('Error processing blockchain event:', error);
      
      // Update event with error
      await BlockchainEvent.update(
        { 
          error_message: error.message,
          retry_count: sequelize.literal('retry_count + 1')
        },
        { where: { transaction_hash: eventData.transaction_hash, event_type: eventData.event_type } }
      );
      
      throw error;
    }
  }

  /**
   * Handle stage transition event
   */
  async handleStageTransition(eventData, txHash) {
    try {
      const { previousStage, newStage, amount } = eventData;
      
      // Update capital recovery if this is a capital pool payment
      if (amount && amount > 0) {
        await capitalRecoveryService.recordPayment(amount, txHash);
      }

      logger.info('Stage transition processed', {
        previousStage,
        newStage,
        amount,
        txHash
      });
    } catch (error) {
      logger.error('Error handling stage transition:', error);
      throw error;
    }
  }

  /**
   * Handle profit distributed event
   */
  async handleProfitDistributed(eventData, txHash) {
    try {
      // This would update distribution records with blockchain confirmation
      logger.info('Profit distribution confirmed on blockchain', {
        eventData,
        txHash
      });
    } catch (error) {
      logger.error('Error handling profit distributed event:', error);
      throw error;
    }
  }

  /**
   * Handle payment recorded event
   */
  async handlePaymentRecorded(eventData, txHash) {
    try {
      const { amount } = eventData;
      
      // Record payment in capital recovery
      await capitalRecoveryService.recordPayment(amount, txHash);
      
      logger.info('Payment recorded from blockchain', {
        amount,
        txHash
      });
    } catch (error) {
      logger.error('Error handling payment recorded event:', error);
      throw error;
    }
  }

  /**
   * Handle token transfer event
   */
  async handleTokenTransfer(eventData, txHash) {
    try {
      // This would update token holdings based on transfers
      // Implementation depends on your specific requirements
      logger.info('Token transfer processed', {
        eventData,
        txHash
      });
    } catch (error) {
      logger.error('Error handling token transfer:', error);
      throw error;
    }
  }

  /**
   * Get blockchain service health status
   */
  getHealthStatus() {
    return {
      initialized: this.isInitialized,
      provider_connected: !!this.provider,
      contracts_loaded: Object.keys(this.contracts).length,
      last_check: new Date()
    };
  }
}

export default new BlockchainService();
