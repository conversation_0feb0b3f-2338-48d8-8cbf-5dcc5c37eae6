import express from 'express';
import { authenticateToken } from '../middlewares/auth.js';
import { logger } from '../utils/logger.js';
import profitSharingService from '../services/profitSharingService.js';
import capitalRecoveryService from '../services/capitalRecoveryService.js';
import blockchainService from '../services/blockchainService.js';
import eventListenerService from '../services/eventListener.js';
import {
  Token,
  TokenHolding,
  Distribution,
  IndividualPayout,
  ProfitSubmission,
  Role,
  BlockchainEvent
} from '../setup/profit_sharing_models.js';
import { User } from '../setup/models.js';
import { sequelize } from '../setup/models.js';

const router = express.Router();

// Middleware to check if user has required role
const requireRole = (roles) => {
  return async (req, res, next) => {
    try {
      const userRoles = await Role.findAll({
        where: { 
          user_id: req.user.id,
          is_active: true 
        }
      });

      const userRoleTypes = userRoles.map(role => role.role_type);
      const hasRequiredRole = roles.some(role => userRoleTypes.includes(role));

      if (!hasRequiredRole) {
        return res.status(403).json({ 
          error: 'Insufficient permissions',
          required_roles: roles,
          user_roles: userRoleTypes
        });
      }

      req.userRoles = userRoleTypes;
      next();
    } catch (error) {
      logger.error('Error checking user roles:', error);
      res.status(500).json({ error: 'Error checking permissions' });
    }
  };
};

// POST /accounting/profit - Submit monthly profit
router.post('/accounting/profit', authenticateToken, requireRole(['plains_north', 'operator']), async (req, res) => {
  try {
    const { gross_profit, net_profit, submission_period, metadata } = req.body;

    // Validate required fields
    if (!gross_profit || !net_profit || !submission_period) {
      return res.status(400).json({
        error: 'Missing required fields: gross_profit, net_profit, submission_period'
      });
    }

    const result = await profitSharingService.submitMonthlyProfit({
      gross_profit,
      net_profit,
      submission_period,
      metadata
    }, req.user.id);

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    logger.error('Error submitting monthly profit:', error);
    res.status(500).json({ 
      error: 'Failed to submit monthly profit',
      message: error.message 
    });
  }
});

// POST /accounting/profit/:id/approve - Approve profit submission
router.post('/accounting/profit/:id/approve', authenticateToken, requireRole(['plains_north']), async (req, res) => {
  try {
    const submissionId = parseInt(req.params.id);
    
    const result = await profitSharingService.approveProfitSubmission(submissionId, req.user.id);

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    logger.error('Error approving profit submission:', error);
    res.status(500).json({ 
      error: 'Failed to approve profit submission',
      message: error.message 
    });
  }
});

// GET /capital/status - Get capital recovery status
router.get('/capital/status', authenticateToken, async (req, res) => {
  try {
    const capitalStatus = await capitalRecoveryService.getCapitalStatus();
    
    res.json({
      success: true,
      data: capitalStatus
    });
  } catch (error) {
    logger.error('Error getting capital status:', error);
    res.status(500).json({ 
      error: 'Failed to get capital status',
      message: error.message 
    });
  }
});

// POST /capital/update - Update capital recovery (manual)
router.post('/capital/update', authenticateToken, requireRole(['plains_north', 'operator']), async (req, res) => {
  try {
    const { amount, tx_hash } = req.body;

    if (!amount || amount <= 0) {
      return res.status(400).json({
        error: 'Invalid amount provided'
      });
    }

    const result = await capitalRecoveryService.recordPayment(
      parseFloat(amount),
      tx_hash,
      req.user.id
    );

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    logger.error('Error updating capital recovery:', error);
    res.status(500).json({ 
      error: 'Failed to update capital recovery',
      message: error.message 
    });
  }
});

// GET /capital/history - Get capital recovery history
router.get('/capital/history', authenticateToken, requireRole(['plains_north', 'operator']), async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 50;
    const offset = parseInt(req.query.offset) || 0;

    const history = await capitalRecoveryService.getCapitalHistory(limit, offset);
    
    res.json({
      success: true,
      data: history
    });
  } catch (error) {
    logger.error('Error getting capital history:', error);
    res.status(500).json({ 
      error: 'Failed to get capital history',
      message: error.message 
    });
  }
});

// GET /capital/statistics - Get capital recovery statistics
router.get('/capital/statistics', authenticateToken, requireRole(['plains_north', 'operator']), async (req, res) => {
  try {
    const statistics = await capitalRecoveryService.getRecoveryStatistics();
    
    res.json({
      success: true,
      data: statistics
    });
  } catch (error) {
    logger.error('Error getting capital statistics:', error);
    res.status(500).json({ 
      error: 'Failed to get capital statistics',
      message: error.message 
    });
  }
});

// GET /token/balance/:investor - Get token balance for investor
router.get('/token/balance/:investor', authenticateToken, async (req, res) => {
  try {
    const investorId = parseInt(req.params.investor);
    
    // Check if user can access this investor's data
    if (req.user.id !== investorId && !req.userRoles?.includes('plains_north')) {
      return res.status(403).json({ error: 'Access denied' });
    }

    const tokenHoldings = await TokenHolding.findAll({
      where: { investor_id: investorId },
      include: [{
        model: Token,
        as: 'token',
        attributes: ['id', 'token_name', 'token_symbol', 'decimals', 'contract_address']
      }]
    });

    res.json({
      success: true,
      data: tokenHoldings
    });
  } catch (error) {
    logger.error('Error getting token balance:', error);
    res.status(500).json({ 
      error: 'Failed to get token balance',
      message: error.message 
    });
  }
});

// GET /payout/history/:investor - Get payout history for investor
router.get('/payout/history/:investor', authenticateToken, async (req, res) => {
  try {
    const investorId = parseInt(req.params.investor);
    const limit = parseInt(req.query.limit) || 50;
    const offset = parseInt(req.query.offset) || 0;
    
    // Check if user can access this investor's data
    if (req.user.id !== investorId && !req.userRoles?.includes('plains_north')) {
      return res.status(403).json({ error: 'Access denied' });
    }

    const payoutHistory = await profitSharingService.getInvestorPayoutHistory(investorId, limit, offset);

    res.json({
      success: true,
      data: payoutHistory
    });
  } catch (error) {
    logger.error('Error getting payout history:', error);
    res.status(500).json({ 
      error: 'Failed to get payout history',
      message: error.message 
    });
  }
});

// GET /distributions - Get distribution history
router.get('/distributions', authenticateToken, requireRole(['plains_north', 'operator']), async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 50;
    const offset = parseInt(req.query.offset) || 0;

    const distributions = await profitSharingService.getDistributionHistory(limit, offset);

    res.json({
      success: true,
      data: distributions
    });
  } catch (error) {
    logger.error('Error getting distribution history:', error);
    res.status(500).json({ 
      error: 'Failed to get distribution history',
      message: error.message 
    });
  }
});

// GET /dashboard/investor/:id - Get investor dashboard data
router.get('/dashboard/investor/:id', authenticateToken, async (req, res) => {
  try {
    const investorId = parseInt(req.params.id);
    
    // Check if user can access this investor's data
    if (req.user.id !== investorId && !req.userRoles?.includes('plains_north')) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Get capital status
    const capitalStatus = await capitalRecoveryService.getCapitalStatus();
    
    // Get token balances
    const tokenBalances = await TokenHolding.findAll({
      where: { investor_id: investorId },
      include: [{
        model: Token,
        as: 'token',
        attributes: ['id', 'token_name', 'token_symbol', 'decimals']
      }]
    });

    // Get recent payouts
    const recentPayouts = await profitSharingService.getInvestorPayoutHistory(investorId, 10, 0);

    // Calculate total received
    const totalReceived = recentPayouts.reduce((sum, payout) => 
      sum + parseFloat(payout.amount || 0), 0);

    res.json({
      success: true,
      data: {
        capital_status: capitalStatus,
        token_balances: tokenBalances,
        recent_payouts: recentPayouts,
        total_received: totalReceived
      }
    });
  } catch (error) {
    logger.error('Error getting investor dashboard:', error);
    res.status(500).json({ 
      error: 'Failed to get investor dashboard',
      message: error.message 
    });
  }
});

// GET /dashboard/admin - Get admin dashboard data
router.get('/dashboard/admin', authenticateToken, requireRole(['plains_north', 'operator']), async (req, res) => {
  try {
    // Get capital status
    const capitalStatus = await capitalRecoveryService.getCapitalStatus();
    
    // Get recent distributions
    const recentDistributions = await profitSharingService.getDistributionHistory(10, 0);
    
    // Get recent profit submissions
    const recentProfitSubmissions = await ProfitSubmission.findAll({
      limit: 10,
      order: [['submission_date', 'DESC']],
      include: [{
        model: User,
        as: 'submittedBy',
        attributes: ['id', 'company_name', 'contact_name']
      }]
    });

    // Get statistics
    const statistics = await profitSharingService.getProfitSharingStatistics();

    // Get KYC statistics
    const kycStats = await User.findAll({
      attributes: [
        'kyc_status',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      group: ['kyc_status']
    });

    const kycStatistics = {
      verified_investors: kycStats.find(s => s.kyc_status === 'verified')?.get('count') || 0,
      pending_kyc: kycStats.find(s => s.kyc_status === 'pending')?.get('count') || 0,
      failed_kyc: kycStats.find(s => s.kyc_status === 'failed')?.get('count') || 0
    };

    res.json({
      success: true,
      data: {
        capital_status: capitalStatus,
        recent_distributions: recentDistributions,
        recent_profit_submissions: recentProfitSubmissions,
        statistics: {
          ...statistics,
          ...kycStatistics
        }
      }
    });
  } catch (error) {
    logger.error('Error getting admin dashboard:', error);
    res.status(500).json({ 
      error: 'Failed to get admin dashboard',
      message: error.message 
    });
  }
});

// POST /kyc/verify - Trigger KYC verification for investors
router.post('/kyc/verify', authenticateToken, requireRole(['plains_north', 'operator']), async (req, res) => {
  try {
    const { investor_id, verified } = req.body;

    if (!investor_id || typeof verified !== 'boolean') {
      return res.status(400).json({
        error: 'Missing required fields: investor_id, verified'
      });
    }

    // Update KYC status in database
    await User.update(
      { kyc_status: verified ? 'verified' : 'failed' },
      { where: { id: investor_id } }
    );

    // Update KYC status on blockchain if configured
    try {
      const investor = await User.findByPk(investor_id);
      if (investor && investor.wallet_address && process.env.BLOCKCHAIN_PRIVATE_KEY) {
        await blockchainService.updateKYCStatus(
          investor.wallet_address,
          verified,
          process.env.BLOCKCHAIN_PRIVATE_KEY
        );
      }
    } catch (blockchainError) {
      logger.warn('Failed to update KYC status on blockchain:', blockchainError);
      // Don't fail the request if blockchain update fails
    }

    res.json({
      success: true,
      data: {
        investor_id,
        kyc_status: verified ? 'verified' : 'failed'
      }
    });
  } catch (error) {
    logger.error('Error updating KYC status:', error);
    res.status(500).json({
      error: 'Failed to update KYC status',
      message: error.message
    });
  }
});

// GET /blockchain/health - Get blockchain service health
router.get('/blockchain/health', authenticateToken, requireRole(['plains_north', 'operator']), async (req, res) => {
  try {
    const blockchainHealth = blockchainService.getHealthStatus();
    const eventListenerHealth = eventListenerService.getHealthStatus();

    res.json({
      success: true,
      data: {
        blockchain_service: blockchainHealth,
        event_listener: eventListenerHealth
      }
    });
  } catch (error) {
    logger.error('Error getting blockchain health:', error);
    res.status(500).json({
      error: 'Failed to get blockchain health',
      message: error.message
    });
  }
});

// POST /blockchain/sync-tokens - Sync token holdings from blockchain
router.post('/blockchain/sync-tokens', authenticateToken, requireRole(['plains_north', 'operator']), async (req, res) => {
  try {
    const result = await blockchainService.syncTokenHoldings();

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    logger.error('Error syncing token holdings:', error);
    res.status(500).json({
      error: 'Failed to sync token holdings',
      message: error.message
    });
  }
});

// POST /events/restart - Restart event listener service
router.post('/events/restart', authenticateToken, requireRole(['plains_north', 'operator']), async (req, res) => {
  try {
    await eventListenerService.restart();

    res.json({
      success: true,
      message: 'Event listener service restarted successfully'
    });
  } catch (error) {
    logger.error('Error restarting event listener:', error);
    res.status(500).json({
      error: 'Failed to restart event listener',
      message: error.message
    });
  }
});

// GET /events/recent - Get recent blockchain events
router.get('/events/recent', authenticateToken, requireRole(['plains_north', 'operator']), async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 50;
    const offset = parseInt(req.query.offset) || 0;
    const eventType = req.query.event_type;

    const whereClause = {};
    if (eventType) {
      whereClause.event_type = eventType;
    }

    const events = await BlockchainEvent.findAll({
      where: whereClause,
      limit,
      offset,
      order: [['created_at', 'DESC']]
    });

    res.json({
      success: true,
      data: events
    });
  } catch (error) {
    logger.error('Error getting recent events:', error);
    res.status(500).json({
      error: 'Failed to get recent events',
      message: error.message
    });
  }
});

// GET /statistics - Get comprehensive profit sharing statistics
router.get('/statistics', authenticateToken, requireRole(['plains_north', 'operator']), async (req, res) => {
  try {
    const profitStats = await profitSharingService.getProfitSharingStatistics();
    const capitalStats = await capitalRecoveryService.getRecoveryStatistics();

    res.json({
      success: true,
      data: {
        profit_sharing: profitStats,
        capital_recovery: capitalStats
      }
    });
  } catch (error) {
    logger.error('Error getting statistics:', error);
    res.status(500).json({
      error: 'Failed to get statistics',
      message: error.message
    });
  }
});

export default router;
