import { Sequelize, DataTypes } from 'sequelize';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Sequelize
const sequelize = new Sequelize(process.env.DATABASE_URL, {
  dialect: 'postgres',
  logging: console.log,
  dialectOptions: {
    ssl: {
      require: true,
      rejectUnauthorized: false
    }
  }
});

// Import existing User model
import { User } from './models.js';

// Role Model
const Role = sequelize.define('Role', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'institutional_investors',
      key: 'id'
    }
  },
  role_type: {
    type: DataTypes.ENUM('plains_north', 'operator', 'licence_holder', 'investor'),
    allowNull: false
  },
  permissions: {
    type: DataTypes.JSONB,
    defaultValue: {}
  },
  assigned_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  assigned_by: {
    type: DataTypes.INTEGER,
    references: {
      model: 'institutional_investors',
      key: 'id'
    }
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  }
}, {
  timestamps: false,
  tableName: 'roles',
  indexes: [
    { fields: ['user_id'] },
    { fields: ['role_type'] },
    { fields: ['is_active'] },
    { unique: true, fields: ['user_id', 'role_type'] }
  ]
});

// Capital Pool Model
const CapitalPool = sequelize.define('CapitalPool', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  target_amount: {
    type: DataTypes.DECIMAL(20, 2),
    allowNull: false,
    defaultValue: 150000.00
  },
  repaid_amount: {
    type: DataTypes.DECIMAL(20, 2),
    allowNull: false,
    defaultValue: 0.00
  },
  stage: {
    type: DataTypes.ENUM('capital_recovery', 'post_recovery'),
    allowNull: false,
    defaultValue: 'capital_recovery'
  },
  remaining_amount: {
    type: DataTypes.VIRTUAL,
    get() {
      return this.target_amount - this.repaid_amount;
    }
  },
  recovery_percentage: {
    type: DataTypes.VIRTUAL,
    get() {
      if (this.target_amount > 0) {
        return Math.min(100, (this.repaid_amount / this.target_amount) * 100);
      }
      return 0;
    }
  },
  last_updated: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_by: {
    type: DataTypes.INTEGER,
    references: {
      model: 'institutional_investors',
      key: 'id'
    }
  },
  blockchain_tx_hash: {
    type: DataTypes.STRING(255)
  },
  metadata: {
    type: DataTypes.JSONB,
    defaultValue: {}
  }
}, {
  timestamps: false,
  tableName: 'capital_pool',
  indexes: [
    { fields: ['stage'] },
    { fields: ['last_updated'] }
  ]
});

// Token Model
const Token = sequelize.define('Token', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  contract_address: {
    type: DataTypes.STRING(255),
    allowNull: false,
    unique: true
  },
  token_name: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  token_symbol: {
    type: DataTypes.STRING(10),
    allowNull: false
  },
  decimals: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 18
  },
  total_supply: {
    type: DataTypes.DECIMAL(30, 0),
    allowNull: false,
    defaultValue: 0
  },
  max_supply: {
    type: DataTypes.DECIMAL(30, 0)
  },
  token_type: {
    type: DataTypes.ENUM('profit_sharing', 'governance', 'utility'),
    allowNull: false,
    defaultValue: 'profit_sharing'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  blockchain_tx_hash: {
    type: DataTypes.STRING(255)
  },
  metadata: {
    type: DataTypes.JSONB,
    defaultValue: {}
  }
}, {
  timestamps: true,
  tableName: 'tokens',
  createdAt: 'created_at',
  updatedAt: false,
  indexes: [
    { fields: ['contract_address'] },
    { fields: ['is_active'] }
  ]
});

// Token Holdings Model
const TokenHolding = sequelize.define('TokenHolding', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  token_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'tokens',
      key: 'id'
    }
  },
  investor_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'institutional_investors',
      key: 'id'
    }
  },
  balance: {
    type: DataTypes.DECIMAL(30, 0),
    allowNull: false,
    defaultValue: 0
  },
  vested_balance: {
    type: DataTypes.DECIMAL(30, 0),
    allowNull: false,
    defaultValue: 0
  },
  vesting_start: {
    type: DataTypes.DATE
  },
  vesting_duration: {
    type: DataTypes.INTEGER // in seconds
  },
  last_updated: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  blockchain_tx_hash: {
    type: DataTypes.STRING(255)
  },
  metadata: {
    type: DataTypes.JSONB,
    defaultValue: {}
  }
}, {
  timestamps: false,
  tableName: 'token_holdings',
  indexes: [
    { fields: ['token_id'] },
    { fields: ['investor_id'] },
    { fields: ['last_updated'] },
    { unique: true, fields: ['token_id', 'investor_id'] }
  ]
});

// Distribution Model
const Distribution = sequelize.define('Distribution', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  distribution_type: {
    type: DataTypes.ENUM('monthly_profit', 'capital_recovery', 'retained_share', 'tokenised_profit'),
    allowNull: false
  },
  total_amount: {
    type: DataTypes.DECIMAL(20, 2),
    allowNull: false
  },
  to_capital_pool: {
    type: DataTypes.DECIMAL(20, 2),
    defaultValue: 0.00
  },
  to_retained_share: {
    type: DataTypes.DECIMAL(20, 2),
    defaultValue: 0.00
  },
  to_tokenised_pool: {
    type: DataTypes.DECIMAL(20, 2),
    defaultValue: 0.00
  },
  stage: {
    type: DataTypes.ENUM('capital_recovery', 'post_recovery'),
    allowNull: false
  },
  distribution_date: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  initiated_by: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'institutional_investors',
      key: 'id'
    }
  },
  blockchain_tx_hash: {
    type: DataTypes.STRING(255)
  },
  status: {
    type: DataTypes.ENUM('pending', 'processing', 'completed', 'failed'),
    allowNull: false,
    defaultValue: 'pending'
  },
  metadata: {
    type: DataTypes.JSONB,
    defaultValue: {}
  }
}, {
  timestamps: true,
  tableName: 'distributions',
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    { fields: ['distribution_type'] },
    { fields: ['stage'] },
    { fields: ['status'] },
    { fields: ['distribution_date'] }
  ]
});

// Individual Payout Model
const IndividualPayout = sequelize.define('IndividualPayout', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  distribution_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'distributions',
      key: 'id'
    }
  },
  investor_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'institutional_investors',
      key: 'id'
    }
  },
  token_id: {
    type: DataTypes.INTEGER,
    references: {
      model: 'tokens',
      key: 'id'
    }
  },
  amount: {
    type: DataTypes.DECIMAL(20, 2),
    allowNull: false
  },
  token_balance_at_time: {
    type: DataTypes.DECIMAL(30, 0)
  },
  total_supply_at_time: {
    type: DataTypes.DECIMAL(30, 0)
  },
  payout_percentage: {
    type: DataTypes.DECIMAL(8, 4)
  },
  blockchain_tx_hash: {
    type: DataTypes.STRING(255)
  },
  status: {
    type: DataTypes.ENUM('pending', 'processing', 'completed', 'failed'),
    allowNull: false,
    defaultValue: 'pending'
  },
  processed_at: {
    type: DataTypes.DATE
  },
  metadata: {
    type: DataTypes.JSONB,
    defaultValue: {}
  }
}, {
  timestamps: true,
  tableName: 'individual_payouts',
  createdAt: 'created_at',
  updatedAt: false,
  indexes: [
    { fields: ['distribution_id'] },
    { fields: ['investor_id'] },
    { fields: ['status'] }
  ]
});

// Profit Submission Model
const ProfitSubmission = sequelize.define('ProfitSubmission', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  submitted_by: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'institutional_investors',
      key: 'id'
    }
  },
  gross_profit: {
    type: DataTypes.DECIMAL(20, 2),
    allowNull: false
  },
  net_profit: {
    type: DataTypes.DECIMAL(20, 2),
    allowNull: false
  },
  plains_north_share: {
    type: DataTypes.DECIMAL(20, 2),
    allowNull: false
  },
  submission_period: {
    type: DataTypes.STRING(7), // YYYY-MM format
    allowNull: false,
    unique: true
  },
  submission_date: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  distribution_id: {
    type: DataTypes.INTEGER,
    references: {
      model: 'distributions',
      key: 'id'
    }
  },
  status: {
    type: DataTypes.ENUM('pending', 'approved', 'distributed', 'rejected'),
    allowNull: false,
    defaultValue: 'pending'
  },
  approved_by: {
    type: DataTypes.INTEGER,
    references: {
      model: 'institutional_investors',
      key: 'id'
    }
  },
  approved_at: {
    type: DataTypes.DATE
  },
  metadata: {
    type: DataTypes.JSONB,
    defaultValue: {}
  }
}, {
  timestamps: false,
  tableName: 'profit_submissions',
  indexes: [
    { fields: ['submission_period'] },
    { fields: ['status'] },
    { fields: ['submission_date'] }
  ]
});

// Blockchain Events Model
const BlockchainEvent = sequelize.define('BlockchainEvent', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  event_type: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  contract_address: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  transaction_hash: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  block_number: {
    type: DataTypes.BIGINT,
    allowNull: false
  },
  block_timestamp: {
    type: DataTypes.DATE,
    allowNull: false
  },
  event_data: {
    type: DataTypes.JSONB,
    allowNull: false
  },
  processed: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  processed_at: {
    type: DataTypes.DATE
  },
  error_message: {
    type: DataTypes.TEXT
  },
  retry_count: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  }
}, {
  timestamps: true,
  tableName: 'blockchain_events',
  createdAt: 'created_at',
  updatedAt: false,
  indexes: [
    { fields: ['event_type'] },
    { fields: ['contract_address'] },
    { fields: ['processed'] },
    { fields: ['block_number'] },
    { unique: true, fields: ['transaction_hash', 'event_type'] }
  ]
});

// Define associations
// User associations
User.hasMany(Role, { foreignKey: 'user_id', as: 'roles' });
Role.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
Role.belongsTo(User, { foreignKey: 'assigned_by', as: 'assignedBy' });

User.hasMany(CapitalPool, { foreignKey: 'updated_by', as: 'capitalPoolUpdates' });
CapitalPool.belongsTo(User, { foreignKey: 'updated_by', as: 'updatedBy' });

User.hasMany(TokenHolding, { foreignKey: 'investor_id', as: 'tokenHoldings' });
TokenHolding.belongsTo(User, { foreignKey: 'investor_id', as: 'investor' });

User.hasMany(Distribution, { foreignKey: 'initiated_by', as: 'initiatedDistributions' });
Distribution.belongsTo(User, { foreignKey: 'initiated_by', as: 'initiatedBy' });

User.hasMany(IndividualPayout, { foreignKey: 'investor_id', as: 'payouts' });
IndividualPayout.belongsTo(User, { foreignKey: 'investor_id', as: 'investor' });

User.hasMany(ProfitSubmission, { foreignKey: 'submitted_by', as: 'profitSubmissions' });
ProfitSubmission.belongsTo(User, { foreignKey: 'submitted_by', as: 'submittedBy' });
ProfitSubmission.belongsTo(User, { foreignKey: 'approved_by', as: 'approvedBy' });

// Token associations
Token.hasMany(TokenHolding, { foreignKey: 'token_id', as: 'holdings' });
TokenHolding.belongsTo(Token, { foreignKey: 'token_id', as: 'token' });

Token.hasMany(IndividualPayout, { foreignKey: 'token_id', as: 'payouts' });
IndividualPayout.belongsTo(Token, { foreignKey: 'token_id', as: 'token' });

// Distribution associations
Distribution.hasMany(IndividualPayout, { foreignKey: 'distribution_id', as: 'payouts' });
IndividualPayout.belongsTo(Distribution, { foreignKey: 'distribution_id', as: 'distribution' });

Distribution.hasOne(ProfitSubmission, { foreignKey: 'distribution_id', as: 'profitSubmission' });
ProfitSubmission.belongsTo(Distribution, { foreignKey: 'distribution_id', as: 'distribution' });

// Sync models with database
async function syncProfitSharingModels() {
  try {
    console.log('Starting profit sharing model synchronization...');
    await sequelize.sync();
    console.log('Profit sharing models synchronized successfully');
  } catch (error) {
    console.error('Error synchronizing profit sharing models:', error);
    throw error;
  }
}

// Export models and database connection
export {
  sequelize,
  Role,
  CapitalPool,
  Token,
  TokenHolding,
  Distribution,
  IndividualPayout,
  ProfitSubmission,
  BlockchainEvent,
  syncProfitSharingModels
};
