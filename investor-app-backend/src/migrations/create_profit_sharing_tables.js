import { pool } from '../setup/databases.js';
import { logger } from '../utils/logger.js';

/**
 * Migration to create profit sharing related tables
 * This includes roles, capital_pool, tokens, distributions, and related tables
 */
export async function createProfitSharingTables() {
  logger.info('Creating profit sharing tables...');
  
  try {
    // Create roles table for Plains North, operator and licence holder assignments
    await pool.query(`
      CREATE TABLE IF NOT EXISTS roles (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES institutional_investors(id) ON DELETE CASCADE,
        role_type VARCHAR(50) NOT NULL CHECK (role_type IN ('plains_north', 'operator', 'licence_holder', 'investor')),
        permissions JSONB DEFAULT '{}',
        assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        assigned_by INTEGER REFERENCES institutional_investors(id),
        is_active BOOLEAN DEFAULT true,
        UNIQUE(user_id, role_type)
      );
    `);

    // Create capital_pool table storing the target amount, repaid balance and stage
    await pool.query(`
      CREATE TABLE IF NOT EXISTS capital_pool (
        id SERIAL PRIMARY KEY,
        target_amount NUMERIC(20,2) NOT NULL DEFAULT 150000.00,
        repaid_amount NUMERIC(20,2) NOT NULL DEFAULT 0.00,
        stage VARCHAR(20) NOT NULL DEFAULT 'capital_recovery' CHECK (stage IN ('capital_recovery', 'post_recovery')),
        remaining_amount NUMERIC(20,2) GENERATED ALWAYS AS (target_amount - repaid_amount) STORED,
        recovery_percentage NUMERIC(5,2) GENERATED ALWAYS AS (
          CASE 
            WHEN target_amount > 0 THEN LEAST(100.00, (repaid_amount / target_amount) * 100)
            ELSE 0.00
          END
        ) STORED,
        last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_by INTEGER REFERENCES institutional_investors(id),
        blockchain_tx_hash VARCHAR(255),
        metadata JSONB DEFAULT '{}'
      );
    `);

    // Insert initial capital pool record if none exists
    await pool.query(`
      INSERT INTO capital_pool (target_amount, repaid_amount, stage)
      SELECT 150000.00, 0.00, 'capital_recovery'
      WHERE NOT EXISTS (SELECT 1 FROM capital_pool);
    `);

    // Create tokens table with metadata for the ERC-20 profit token and holdings per investor
    await pool.query(`
      CREATE TABLE IF NOT EXISTS tokens (
        id SERIAL PRIMARY KEY,
        contract_address VARCHAR(255) NOT NULL,
        token_name VARCHAR(255) NOT NULL,
        token_symbol VARCHAR(10) NOT NULL,
        decimals INTEGER NOT NULL DEFAULT 18,
        total_supply NUMERIC(30,0) NOT NULL DEFAULT 0,
        max_supply NUMERIC(30,0),
        token_type VARCHAR(50) NOT NULL DEFAULT 'profit_sharing' CHECK (token_type IN ('profit_sharing', 'governance', 'utility')),
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        blockchain_tx_hash VARCHAR(255),
        metadata JSONB DEFAULT '{}',
        UNIQUE(contract_address)
      );
    `);

    // Create token_holdings table for tracking individual investor holdings
    await pool.query(`
      CREATE TABLE IF NOT EXISTS token_holdings (
        id SERIAL PRIMARY KEY,
        token_id INTEGER NOT NULL REFERENCES tokens(id) ON DELETE CASCADE,
        investor_id INTEGER NOT NULL REFERENCES institutional_investors(id) ON DELETE CASCADE,
        balance NUMERIC(30,0) NOT NULL DEFAULT 0,
        vested_balance NUMERIC(30,0) NOT NULL DEFAULT 0,
        vesting_start TIMESTAMP,
        vesting_duration INTEGER, -- in seconds
        last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        blockchain_tx_hash VARCHAR(255),
        metadata JSONB DEFAULT '{}',
        UNIQUE(token_id, investor_id)
      );
    `);

    // Create distributions table logging each monthly payout with transaction hashes
    await pool.query(`
      CREATE TABLE IF NOT EXISTS distributions (
        id SERIAL PRIMARY KEY,
        distribution_type VARCHAR(50) NOT NULL CHECK (distribution_type IN ('monthly_profit', 'capital_recovery', 'retained_share', 'tokenised_profit')),
        total_amount NUMERIC(20,2) NOT NULL,
        to_capital_pool NUMERIC(20,2) DEFAULT 0.00,
        to_retained_share NUMERIC(20,2) DEFAULT 0.00,
        to_tokenised_pool NUMERIC(20,2) DEFAULT 0.00,
        stage VARCHAR(20) NOT NULL CHECK (stage IN ('capital_recovery', 'post_recovery')),
        distribution_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        initiated_by INTEGER NOT NULL REFERENCES institutional_investors(id),
        blockchain_tx_hash VARCHAR(255),
        status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
        metadata JSONB DEFAULT '{}',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Create individual_payouts table for tracking payouts to specific investors
    await pool.query(`
      CREATE TABLE IF NOT EXISTS individual_payouts (
        id SERIAL PRIMARY KEY,
        distribution_id INTEGER NOT NULL REFERENCES distributions(id) ON DELETE CASCADE,
        investor_id INTEGER NOT NULL REFERENCES institutional_investors(id) ON DELETE CASCADE,
        token_id INTEGER REFERENCES tokens(id),
        amount NUMERIC(20,2) NOT NULL,
        token_balance_at_time NUMERIC(30,0),
        total_supply_at_time NUMERIC(30,0),
        payout_percentage NUMERIC(8,4),
        blockchain_tx_hash VARCHAR(255),
        status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
        processed_at TIMESTAMP,
        metadata JSONB DEFAULT '{}',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Create profit_submissions table for tracking monthly profit inputs
    await pool.query(`
      CREATE TABLE IF NOT EXISTS profit_submissions (
        id SERIAL PRIMARY KEY,
        submitted_by INTEGER NOT NULL REFERENCES institutional_investors(id),
        gross_profit NUMERIC(20,2) NOT NULL,
        net_profit NUMERIC(20,2) NOT NULL,
        plains_north_share NUMERIC(20,2) NOT NULL,
        submission_period VARCHAR(7) NOT NULL, -- YYYY-MM format
        submission_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        distribution_id INTEGER REFERENCES distributions(id),
        status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'distributed', 'rejected')),
        approved_by INTEGER REFERENCES institutional_investors(id),
        approved_at TIMESTAMP,
        metadata JSONB DEFAULT '{}',
        UNIQUE(submission_period)
      );
    `);

    // Create blockchain_events table for tracking smart contract events
    await pool.query(`
      CREATE TABLE IF NOT EXISTS blockchain_events (
        id SERIAL PRIMARY KEY,
        event_type VARCHAR(100) NOT NULL,
        contract_address VARCHAR(255) NOT NULL,
        transaction_hash VARCHAR(255) NOT NULL,
        block_number BIGINT NOT NULL,
        block_timestamp TIMESTAMP NOT NULL,
        event_data JSONB NOT NULL,
        processed BOOLEAN DEFAULT false,
        processed_at TIMESTAMP,
        error_message TEXT,
        retry_count INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(transaction_hash, event_type)
      );
    `);

    // Create indexes for better query performance (create them individually to handle errors gracefully)
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_roles_user_id ON roles(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_roles_role_type ON roles(role_type)',
      'CREATE INDEX IF NOT EXISTS idx_roles_is_active ON roles(is_active)',
      'CREATE INDEX IF NOT EXISTS idx_capital_pool_stage ON capital_pool(stage)',
      'CREATE INDEX IF NOT EXISTS idx_capital_pool_last_updated ON capital_pool(last_updated)',
      'CREATE INDEX IF NOT EXISTS idx_tokens_contract_address ON tokens(contract_address)',
      'CREATE INDEX IF NOT EXISTS idx_tokens_is_active ON tokens(is_active)',
      'CREATE INDEX IF NOT EXISTS idx_token_holdings_token_id ON token_holdings(token_id)',
      'CREATE INDEX IF NOT EXISTS idx_token_holdings_investor_id ON token_holdings(investor_id)',
      'CREATE INDEX IF NOT EXISTS idx_token_holdings_last_updated ON token_holdings(last_updated)',
      'CREATE INDEX IF NOT EXISTS idx_distributions_distribution_type ON distributions(distribution_type)',
      'CREATE INDEX IF NOT EXISTS idx_distributions_stage ON distributions(stage)',
      'CREATE INDEX IF NOT EXISTS idx_distributions_status ON distributions(status)',
      'CREATE INDEX IF NOT EXISTS idx_distributions_distribution_date ON distributions(distribution_date)',
      'CREATE INDEX IF NOT EXISTS idx_individual_payouts_distribution_id ON individual_payouts(distribution_id)',
      'CREATE INDEX IF NOT EXISTS idx_individual_payouts_investor_id ON individual_payouts(investor_id)',
      'CREATE INDEX IF NOT EXISTS idx_individual_payouts_status ON individual_payouts(status)',
      'CREATE INDEX IF NOT EXISTS idx_profit_submissions_submission_period ON profit_submissions(submission_period)',
      'CREATE INDEX IF NOT EXISTS idx_profit_submissions_status ON profit_submissions(status)',
      'CREATE INDEX IF NOT EXISTS idx_profit_submissions_submission_date ON profit_submissions(submission_date)',
      'CREATE INDEX IF NOT EXISTS idx_blockchain_events_event_type ON blockchain_events(event_type)',
      'CREATE INDEX IF NOT EXISTS idx_blockchain_events_contract_address ON blockchain_events(contract_address)',
      'CREATE INDEX IF NOT EXISTS idx_blockchain_events_processed ON blockchain_events(processed)',
      'CREATE INDEX IF NOT EXISTS idx_blockchain_events_block_number ON blockchain_events(block_number)'
    ];

    for (const indexQuery of indexes) {
      try {
        await pool.query(indexQuery);
      } catch (error) {
        logger.warn(`Failed to create index: ${indexQuery}`, error.message);
        // Continue with other indexes even if one fails
      }
    }

    logger.info('Profit sharing tables created successfully');
    return true;
  } catch (error) {
    logger.error('Error creating profit sharing tables:', error);
    throw error;
  }
}

// Run migration if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  createProfitSharingTables()
    .then(() => {
      logger.info('Migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Migration failed:', error);
      process.exit(1);
    });
}
