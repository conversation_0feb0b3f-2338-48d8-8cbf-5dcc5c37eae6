import { describe, it, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import request from 'supertest';
import app from '../../src/server.js';
import { pool } from '../../src/setup/databases.js';
import { createProfitSharingTables } from '../../src/migrations/create_profit_sharing_tables.js';
import { User } from '../../src/setup/models.js';
import { 
  Role, 
  CapitalPool, 
  Token, 
  TokenHolding, 
  ProfitSubmission,
  Distribution 
} from '../../src/setup/profit_sharing_models.js';

describe('Profit Sharing Integration Tests', () => {
  let authToken;
  let adminToken;
  let testUser;
  let adminUser;
  let testToken;

  beforeAll(async () => {
    // Ensure profit sharing tables exist
    await createProfitSharingTables();
    
    // Create test users
    testUser = await User.create({
      email: '<EMAIL>',
      password: 'hashedpassword',
      company_name: 'Test Investor Corp',
      contact_name: '<PERSON>',
      kyc_status: 'verified'
    });

    adminUser = await User.create({
      email: '<EMAIL>',
      password: 'hashedpassword',
      company_name: 'Plains North Capital',
      contact_name: 'Admin User',
      kyc_status: 'verified'
    });

    // Assign roles
    await Role.create({
      user_id: adminUser.id,
      role_type: 'plains_north',
      is_active: true
    });

    await Role.create({
      user_id: testUser.id,
      role_type: 'investor',
      is_active: true
    });

    // Create test token
    testToken = await Token.create({
      contract_address: '0x1234567890123456789012345678901234567890',
      token_name: 'Test Profit Token',
      token_symbol: 'TPT',
      decimals: 18,
      total_supply: '1000000000000000000000000', // 1M tokens
      token_type: 'profit_sharing',
      is_active: true
    });

    // Create token holding for test user
    await TokenHolding.create({
      token_id: testToken.id,
      investor_id: testUser.id,
      balance: '100000000000000000000', // 100 tokens
      last_updated: new Date()
    });

    // Mock authentication tokens (you'll need to implement actual auth)
    authToken = 'mock-auth-token-investor';
    adminToken = 'mock-admin-token';
  });

  afterAll(async () => {
    // Clean up test data
    await TokenHolding.destroy({ where: { investor_id: testUser.id } });
    await Token.destroy({ where: { id: testToken.id } });
    await Role.destroy({ where: { user_id: [testUser.id, adminUser.id] } });
    await User.destroy({ where: { id: [testUser.id, adminUser.id] } });
    
    // Close database connection
    await pool.end();
  });

  beforeEach(async () => {
    // Reset capital pool to initial state
    await CapitalPool.destroy({ where: {} });
    await CapitalPool.create({
      target_amount: 150000.00,
      repaid_amount: 0.00,
      stage: 'capital_recovery'
    });
  });

  describe('Capital Recovery Endpoints', () => {
    it('should get capital recovery status', async () => {
      const response = await request(app)
        .get('/profit-sharing/capital/status')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('target_amount', 150000);
      expect(response.body.data).toHaveProperty('repaid_amount', 0);
      expect(response.body.data).toHaveProperty('stage', 'capital_recovery');
      expect(response.body.data).toHaveProperty('recovery_percentage', 0);
    });

    it('should update capital recovery (admin only)', async () => {
      const updateData = {
        amount: 50000,
        tx_hash: '0xabcdef1234567890'
      };

      const response = await request(app)
        .post('/profit-sharing/capital/update')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.capitalStatus.repaid_amount).toBe(50000);
      expect(response.body.data.capitalStatus.recovery_percentage).toBeCloseTo(33.33, 1);
    });

    it('should reject capital update from non-admin', async () => {
      const updateData = {
        amount: 50000
      };

      await request(app)
        .post('/profit-sharing/capital/update')
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(403);
    });

    it('should transition to post-recovery stage when target reached', async () => {
      const updateData = {
        amount: 150000,
        tx_hash: '0xfullrecovery123'
      };

      const response = await request(app)
        .post('/profit-sharing/capital/update')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.data.stageChanged).toBe(true);
      expect(response.body.data.newStage).toBe('post_recovery');
    });
  });

  describe('Profit Submission Endpoints', () => {
    it('should submit monthly profit', async () => {
      const profitData = {
        gross_profit: 300000,
        net_profit: 150000,
        submission_period: '2024-01'
      };

      const response = await request(app)
        .post('/profit-sharing/accounting/profit')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(profitData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.submission).toHaveProperty('id');
      expect(response.body.data.plains_north_share).toBeCloseTo(50000, 2);
    });

    it('should reject duplicate submission for same period', async () => {
      const profitData = {
        gross_profit: 300000,
        net_profit: 150000,
        submission_period: '2024-01'
      };

      // First submission should succeed
      await request(app)
        .post('/profit-sharing/accounting/profit')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(profitData)
        .expect(200);

      // Second submission for same period should fail
      await request(app)
        .post('/profit-sharing/accounting/profit')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(profitData)
        .expect(500);
    });

    it('should approve profit submission and trigger distribution', async () => {
      // First create a submission
      const profitData = {
        gross_profit: 300000,
        net_profit: 150000,
        submission_period: '2024-02'
      };

      const submitResponse = await request(app)
        .post('/profit-sharing/accounting/profit')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(profitData)
        .expect(200);

      const submissionId = submitResponse.body.data.submission.id;

      // Then approve it
      const approveResponse = await request(app)
        .post(`/profit-sharing/accounting/profit/${submissionId}/approve`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(approveResponse.body.success).toBe(true);
      expect(approveResponse.body.data.distribution).toHaveProperty('id');
    });
  });

  describe('Token Balance Endpoints', () => {
    it('should get token balance for investor', async () => {
      const response = await request(app)
        .get(`/profit-sharing/token/balance/${testUser.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0]).toHaveProperty('balance', '100000000000000000000');
      expect(response.body.data[0].token).toHaveProperty('token_symbol', 'TPT');
    });

    it('should reject access to other investor\'s balance', async () => {
      const otherUser = await User.create({
        email: '<EMAIL>',
        password: 'hashedpassword',
        company_name: 'Other Corp',
        contact_name: 'Other User'
      });

      await request(app)
        .get(`/profit-sharing/token/balance/${otherUser.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(403);

      await User.destroy({ where: { id: otherUser.id } });
    });
  });

  describe('Dashboard Endpoints', () => {
    it('should get investor dashboard data', async () => {
      const response = await request(app)
        .get(`/profit-sharing/dashboard/investor/${testUser.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('capital_status');
      expect(response.body.data).toHaveProperty('token_balances');
      expect(response.body.data).toHaveProperty('recent_payouts');
      expect(response.body.data).toHaveProperty('total_received');
    });

    it('should get admin dashboard data', async () => {
      const response = await request(app)
        .get('/profit-sharing/dashboard/admin')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('capital_status');
      expect(response.body.data).toHaveProperty('recent_distributions');
      expect(response.body.data).toHaveProperty('recent_profit_submissions');
      expect(response.body.data).toHaveProperty('statistics');
    });

    it('should reject admin dashboard access from non-admin', async () => {
      await request(app)
        .get('/profit-sharing/dashboard/admin')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(403);
    });
  });

  describe('KYC Verification Endpoints', () => {
    it('should update KYC status (admin only)', async () => {
      const kycData = {
        investor_id: testUser.id,
        verified: true
      };

      const response = await request(app)
        .post('/profit-sharing/kyc/verify')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(kycData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.kyc_status).toBe('verified');
    });

    it('should reject KYC update from non-admin', async () => {
      const kycData = {
        investor_id: testUser.id,
        verified: true
      };

      await request(app)
        .post('/profit-sharing/kyc/verify')
        .set('Authorization', `Bearer ${authToken}`)
        .send(kycData)
        .expect(403);
    });
  });

  describe('End-to-End Profit Distribution Flow', () => {
    it('should complete full profit distribution cycle', async () => {
      // Step 1: Submit profit
      const profitData = {
        gross_profit: 600000,
        net_profit: 300000,
        submission_period: '2024-03'
      };

      const submitResponse = await request(app)
        .post('/profit-sharing/accounting/profit')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(profitData)
        .expect(200);

      const submissionId = submitResponse.body.data.submission.id;
      const plainsNorthShare = submitResponse.body.data.plains_north_share;

      expect(plainsNorthShare).toBeCloseTo(100000, 2);

      // Step 2: Approve submission (triggers distribution)
      const approveResponse = await request(app)
        .post(`/profit-sharing/accounting/profit/${submissionId}/approve`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      const distribution = approveResponse.body.data.distribution;
      expect(distribution).toHaveProperty('id');
      expect(distribution.total_amount).toBeCloseTo(100000, 2);

      // Step 3: Verify capital recovery was updated
      const capitalResponse = await request(app)
        .get('/profit-sharing/capital/status')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(capitalResponse.body.data.repaid_amount).toBeCloseTo(100000, 2);
      expect(capitalResponse.body.data.recovery_percentage).toBeCloseTo(66.67, 1);

      // Step 4: Check distribution history
      const distributionsResponse = await request(app)
        .get('/profit-sharing/distributions')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(distributionsResponse.body.data).toHaveLength(1);
      expect(distributionsResponse.body.data[0].total_amount).toBeCloseTo(100000, 2);
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid profit submission data', async () => {
      const invalidData = {
        gross_profit: -1000,
        net_profit: 'invalid',
        submission_period: 'invalid-period'
      };

      await request(app)
        .post('/profit-sharing/accounting/profit')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(invalidData)
        .expect(400);
    });

    it('should handle missing authentication', async () => {
      await request(app)
        .get('/profit-sharing/capital/status')
        .expect(401);
    });

    it('should handle database connection errors gracefully', async () => {
      // This would require mocking database failures
      // Implementation depends on your error handling strategy
    });
  });
});
