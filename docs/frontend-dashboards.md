# Frontend Dashboards Documentation

## Overview

The Archimedes Finance frontend includes comprehensive dashboards for both investors and administrators to manage and monitor the profit-sharing tokenization system. Built with Vue 3, Pinia, and Bootstrap 5, the dashboards provide real-time updates through blockchain event listening and responsive design for all devices.

## Architecture

### Technology Stack
- **Vue 3** - Progressive JavaScript framework with Composition API
- **Pinia** - State management for Vue applications
- **Vue Router** - Client-side routing
- **Bootstrap 5** - CSS framework for responsive design
- **ethers.js** - Ethereum blockchain interaction
- **Vue Toastification** - Toast notifications
- **Axios** - HTTP client for API calls

### Project Structure
```
src/
├── components/
│   └── profit-sharing/
│       ├── KpiCard.vue           # Reusable KPI display component
│       ├── CapitalProgress.vue   # Capital recovery progress visualization
│       ├── DataTable.vue         # Sortable, searchable data table
│       ├── KycPanel.vue          # KYC verification status and controls
│       └── TxHashLink.vue        # Blockchain transaction hash links
├── views/
│   ├── InvestorDashboard.vue     # Investor-facing dashboard
│   ├── AdminDashboard.vue        # Administrator dashboard
│   └── NotFound.vue              # 404 error page
├── stores/
│   └── profitSharing.js          # Pinia store for profit sharing state
├── services/
│   └── eventListener.js          # Blockchain event monitoring
├── api/
│   └── profitSharing.js          # API client for backend communication
└── router/
    └── index.js                  # Vue Router configuration
```

## Components

### KpiCard.vue
Reusable component for displaying key performance indicators with support for:
- Multiple data formats (currency, percentage, number, text)
- Loading states with skeleton animations
- Change indicators with color coding
- Custom icons and variants
- Responsive design

**Props:**
- `title` - Card title
- `value` - Main value to display
- `format` - Data format (currency, percentage, number, text)
- `icon` - Bootstrap icon class
- `loading` - Loading state
- `variant` - Color variant (success, warning, danger, info)

### CapitalProgress.vue
Specialized component for visualizing capital recovery progress featuring:
- Animated progress bar with gradient fills
- Milestone markers at 25%, 50%, 75%, and 100%
- Detailed breakdown of target, repaid, and remaining amounts
- Optional timeline view with completion status
- Stage indicator (Capital Recovery vs Post-Recovery)

**Props:**
- `targetAmount` - Total capital recovery target
- `repaidAmount` - Amount already repaid
- `stage` - Current recovery stage
- `showTimeline` - Whether to display milestone timeline

### DataTable.vue
Comprehensive data table component with:
- Sortable columns with visual indicators
- Search functionality across all columns
- Pagination with configurable page sizes
- Custom cell rendering via slots
- Loading states and empty state handling
- Responsive design with horizontal scrolling

**Features:**
- Column configuration with sorting, alignment, and formatting
- Custom cell templates using named slots
- Built-in search with highlighting
- Pagination controls with page navigation
- Export capabilities (future enhancement)

### KycPanel.vue
KYC verification management component providing:
- Verification status display with color-coded badges
- Document requirements checklist
- Integration with KYC providers (Sumsub, Stripe Identity)
- Progress tracking and status updates
- Admin approval/rejection controls

**States:**
- `unverified` - Initial state, shows requirements
- `pending` - Under review, shows progress
- `verified` - Completed successfully
- `rejected` - Failed verification with retry option

### TxHashLink.vue
Blockchain transaction hash display component featuring:
- Automatic block explorer link generation
- Network-specific styling and URLs
- Hash truncation with full hash on hover
- Copy-to-clipboard functionality
- Support for multiple networks (Ethereum, Polygon, BSC, etc.)

## Views

### InvestorDashboard.vue
Comprehensive dashboard for individual investors displaying:

**Capital Recovery Section:**
- Current stage indicator (Capital Recovery/Post-Recovery)
- Progress bar showing percentage of $150k CAD recovered
- Remaining amount and timeline projections

**Token Holdings:**
- APT token balance and vested amounts
- KYC verification status
- Contract address and token metadata

**Payout History:**
- Chronological list of received distributions
- Transaction hashes with block explorer links
- Amount, date, and status for each payout
- Search and filtering capabilities

**KYC Verification:**
- Current verification status
- Document upload interface
- Provider integration (Sumsub/Stripe)
- Progress tracking and notifications

### AdminDashboard.vue
Administrative interface for managing the profit-sharing system:

**Capital Pool Management:**
- Real-time capital recovery status
- Target vs. actual amounts with visual progress
- Stage transition monitoring

**Monthly Profit Submission:**
- Form for entering net profit figures
- Automatic calculation of Plains North's 1/3 share
- Distribution preview based on current stage
- Confirmation modal with breakdown

**Investor Management:**
- List of pending KYC verifications
- Approve/reject verification controls
- Investor statistics and overview

**Distribution Monitoring:**
- Recent profit distributions with amounts
- Transaction hashes and block explorer links
- Status tracking (pending, completed, failed)
- Historical profit submission records

## State Management

### Pinia Store (profitSharing.js)
Centralized state management for profit-sharing data:

**State:**
- `capitalStatus` - Capital recovery progress and stage
- `investorDashboard` - Investor-specific data
- `adminDashboard` - Administrative overview data
- `loading` - Loading states for different operations
- `errors` - Error handling and display

**Actions:**
- `fetchCapitalStatus()` - Get current capital recovery status
- `fetchInvestorDashboard(id)` - Load investor-specific data
- `fetchAdminDashboard()` - Load administrative overview
- `submitMonthlyProfit(data)` - Submit profit for distribution
- `initiateKYC(investorId)` - Start KYC verification process
- `updateKYCStatus(investorId, status)` - Update verification status

**Computed Properties:**
- `isCapitalRecoveryStage` - Boolean for current stage
- `capitalRecoveryProgress` - Percentage completion
- `formattedCapitalStatus` - Formatted currency values
- `totalTokenBalance` - Sum of all token holdings

## API Integration

### API Client (profitSharing.js)
Axios-based client for backend communication:

**Endpoints:**
- `GET /profit-sharing/dashboard/investor/:id` - Investor dashboard data
- `GET /profit-sharing/dashboard/admin` - Admin dashboard data
- `GET /profit-sharing/capital/status` - Capital recovery status
- `POST /profit-sharing/accounting/profit` - Submit monthly profit
- `POST /profit-sharing/kyc/initiate` - Start KYC verification
- `GET /profit-sharing/events/recent` - Recent blockchain events

**Features:**
- Automatic authentication token injection
- Response interceptors for error handling
- Request/response logging in development
- Retry logic for failed requests

## Blockchain Integration

### Event Listener (eventListener.js)
Real-time blockchain event monitoring:

**Monitored Events:**
- `StageTransition` - Capital recovery stage changes
- `PaymentRecorded` - Capital recovery payments
- `PayoutSent` - Individual investor payouts
- `ProfitDistributed` - Monthly profit distributions
- `KYCStatusUpdated` - Verification status changes

**Features:**
- WebSocket connection with HTTP fallback
- Automatic reconnection with exponential backoff
- Toast notifications for important events
- Automatic dashboard data refresh
- Error handling and connection monitoring

## Routing

### Vue Router Configuration
Protected routes with role-based access control:

**Public Routes:**
- `/` - Landing page
- `/login` - User authentication
- `/register` - Account creation
- `/help` - Help and support

**Protected Routes:**
- `/dashboard` - General dashboard (redirects based on role)
- `/dashboard/investor` - Investor-specific dashboard
- `/dashboard/admin` - Administrator dashboard (manager role required)
- `/profile` - User profile management
- `/messages` - Internal messaging system

**Route Guards:**
- Authentication verification
- Role-based access control
- Automatic redirects for unauthorized access
- Page title management

## Styling and Theming

### Design System
- **Color Palette:** Blue-based theme with web3/DeFi aesthetics
- **Typography:** System fonts with clear hierarchy
- **Components:** Bootstrap 5 with custom overrides
- **Dark Mode:** Full support with CSS custom properties
- **Responsive:** Mobile-first design with breakpoints

### Custom CSS Features
- Smooth animations and transitions
- Loading skeleton animations
- Hover effects and micro-interactions
- Print-friendly styles
- Accessibility improvements (focus indicators, screen reader support)

## Environment Configuration

### Frontend Environment Variables
```bash
# API Configuration
VITE_API_BASE_URL=http://localhost:3000

# Blockchain Configuration
VITE_ETH_RPC_URL=https://sepolia.infura.io/v3/PROJECT_ID
VITE_ETH_WS_URL=wss://sepolia.infura.io/ws/v3/PROJECT_ID
VITE_BLOCKCHAIN_NETWORK=ethereum
VITE_IS_TESTNET=true

# Smart Contract Addresses
VITE_PROFIT_SPLITTER_ADDRESS=0x...
VITE_CAPITAL_RECOVERY_TRACKER_ADDRESS=0x...
VITE_TOKEN_DISTRIBUTOR_ADDRESS=0x...
VITE_PROFIT_TOKEN_ADDRESS=0x...

# KYC Provider
VITE_KYC_PROVIDER=sumsub
VITE_SUMSUB_APP_TOKEN=your_token

# Feature Flags
VITE_ENABLE_PROFIT_SHARING=true
VITE_ENABLE_KYC_VERIFICATION=true
VITE_ENABLE_BLOCKCHAIN_EVENTS=true
```

## Development Workflow

### Local Development
1. Install dependencies: `npm install`
2. Configure environment variables in `.env.local`
3. Start development server: `npm run dev`
4. Access application at `http://localhost:5173`

### Building for Production
1. Build application: `npm run build`
2. Preview production build: `npm run preview`
3. Deploy `dist/` directory to web server

### Testing
- Unit tests: `npm run test:unit`
- E2E tests: `npm run test:e2e`
- Linting: `npm run lint`

## Security Considerations

### Authentication
- JWT token storage in localStorage
- Automatic token refresh
- Secure logout with token cleanup

### API Security
- HTTPS enforcement in production
- Request/response validation
- Rate limiting on sensitive endpoints

### Blockchain Security
- Read-only contract interactions
- Transaction verification before display
- Secure private key handling (server-side only)

## Performance Optimizations

### Code Splitting
- Route-based lazy loading
- Component-level code splitting
- Dynamic imports for large dependencies

### Caching
- API response caching
- Static asset caching
- Service worker for offline functionality

### Bundle Optimization
- Tree shaking for unused code
- Asset compression and minification
- CDN integration for static assets

## Future Enhancements

### Planned Features
- Real-time notifications via WebSockets
- Advanced analytics and reporting
- Mobile application (React Native/Flutter)
- Multi-language support (i18n)
- Advanced charting and data visualization

### Technical Improvements
- Progressive Web App (PWA) capabilities
- Enhanced accessibility features
- Performance monitoring and analytics
- Automated testing coverage
- CI/CD pipeline integration
