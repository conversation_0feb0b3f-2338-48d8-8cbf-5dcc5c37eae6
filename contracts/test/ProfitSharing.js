const {
  time,
  loadFixture,
} = require("@nomicfoundation/hardhat-toolbox/network-helpers");
const { anyValue } = require("@nomicfoundation/hardhat-chai-matchers/withArgs");
const { expect } = require("chai");

describe("Archimedes Finance Profit Sharing", function () {
  // Fixture to deploy all contracts with integrated ownership
  async function deployProfitSharingFixture() {
    const [owner, investor1, investor2, capitalPool, retainedShare] = await ethers.getSigners();

    // Deploy ProfitToken
    const ProfitToken = await ethers.getContractFactory("ProfitToken");
    const profitToken = await ProfitToken.deploy();

    // Deploy CapitalRecoveryTracker with smaller target for testing
    const CapitalRecoveryTracker = await ethers.getContractFactory("CapitalRecoveryTracker");
    const testTargetAmount = ethers.parseEther("100"); // 100 ETH for testing
    const capitalRecoveryTracker = await CapitalRecoveryTracker.deploy(testTargetAmount);

    // Deploy TokenDistributor
    const TokenDistributor = await ethers.getContractFactory("TokenDistributor");
    const tokenDistributor = await TokenDistributor.deploy(await profitToken.getAddress());

    // Deploy ProfitSplitter
    const ProfitSplitter = await ethers.getContractFactory("ProfitSplitter");
    const profitSplitter = await ProfitSplitter.deploy(
      await capitalRecoveryTracker.getAddress(),
      await tokenDistributor.getAddress(),
      capitalPool.address,
      retainedShare.address
    );

    // Transfer ownership of CapitalRecoveryTracker to ProfitSplitter
    await capitalRecoveryTracker.transferOwnership(await profitSplitter.getAddress());

    return {
      profitToken,
      capitalRecoveryTracker,
      tokenDistributor,
      profitSplitter,
      owner,
      investor1,
      investor2,
      capitalPool,
      retainedShare
    };
  }

  // Fixture for standalone CapitalRecoveryTracker tests
  async function deployStandaloneCapitalRecoveryFixture() {
    const [owner, investor1, investor2] = await ethers.getSigners();

    // Deploy CapitalRecoveryTracker with default target
    const CapitalRecoveryTracker = await ethers.getContractFactory("CapitalRecoveryTracker");
    const capitalRecoveryTracker = await CapitalRecoveryTracker.deploy(0); // Use default

    return {
      capitalRecoveryTracker,
      owner,
      investor1,
      investor2
    };
  }

  describe("ProfitToken", function () {
    it("Should deploy with correct name and symbol", async function () {
      const { profitToken } = await loadFixture(deployProfitSharingFixture);
      
      expect(await profitToken.name()).to.equal("Archimedes Profit Token");
      expect(await profitToken.symbol()).to.equal("APT");
    });

    it("Should mint initial supply to owner", async function () {
      const { profitToken, owner } = await loadFixture(deployProfitSharingFixture);
      
      const initialSupply = ethers.parseEther("100000"); // 100k tokens
      expect(await profitToken.balanceOf(owner.address)).to.equal(initialSupply);
    });

    it("Should require KYC verification for transfers", async function () {
      const { profitToken, owner, investor1 } = await loadFixture(deployProfitSharingFixture);
      
      const amount = ethers.parseEther("1000");
      
      // Should fail without KYC
      await expect(
        profitToken.transfer(investor1.address, amount)
      ).to.be.revertedWith("Account not KYC verified");
      
      // Update KYC status
      await profitToken.updateKYCStatus(owner.address, true);
      await profitToken.updateKYCStatus(investor1.address, true);
      
      // Should succeed with KYC
      await expect(profitToken.transfer(investor1.address, amount)).not.to.be.reverted;
    });

    it("Should handle vesting correctly", async function () {
      const { profitToken, owner, investor1 } = await loadFixture(deployProfitSharingFixture);
      
      // Set up KYC
      await profitToken.updateKYCStatus(owner.address, true);
      await profitToken.updateKYCStatus(investor1.address, true);
      
      // Transfer tokens to investor
      const amount = ethers.parseEther("1000");
      await profitToken.transfer(investor1.address, amount);
      
      // Set up vesting (1 year)
      const vestingDuration = 365 * 24 * 60 * 60; // 1 year in seconds
      await profitToken.setupVesting(investor1.address, amount, vestingDuration);
      
      // Initially, no tokens should be vested
      expect(await profitToken.getVestedAmount(investor1.address)).to.equal(0);
      
      // After half the vesting period, half should be vested
      await time.increase(vestingDuration / 2);
      const halfVested = await profitToken.getVestedAmount(investor1.address);
      expect(halfVested).to.be.closeTo(amount / 2n, ethers.parseEther("10")); // Allow small variance
    });
  });

  describe("CapitalRecoveryTracker", function () {
    it("Should start in CapitalRecovery stage", async function () {
      const { capitalRecoveryTracker } = await loadFixture(deployStandaloneCapitalRecoveryFixture);

      expect(await capitalRecoveryTracker.getStage()).to.equal(0); // CapitalRecovery
    });

    it("Should record payments and transition stages", async function () {
      const { capitalRecoveryTracker } = await loadFixture(deployStandaloneCapitalRecoveryFixture);

      const targetAmount = await capitalRecoveryTracker.TARGET_AMOUNT();

      // Record payment less than target
      const payment1 = ethers.parseEther("50000");
      await expect(capitalRecoveryTracker.recordPayment(payment1))
        .to.emit(capitalRecoveryTracker, "PaymentRecorded")
        .withArgs(payment1, payment1, anyValue);

      expect(await capitalRecoveryTracker.getStage()).to.equal(0); // Still CapitalRecovery

      // Record payment that reaches target
      const payment2 = ethers.parseEther("100000");
      await expect(capitalRecoveryTracker.recordPayment(payment2))
        .to.emit(capitalRecoveryTracker, "StageTransition")
        .withArgs(0, 1, anyValue); // CapitalRecovery to PostRecovery

      expect(await capitalRecoveryTracker.getStage()).to.equal(1); // PostRecovery
    });

    it("Should calculate remaining amount correctly", async function () {
      const { capitalRecoveryTracker } = await loadFixture(deployStandaloneCapitalRecoveryFixture);

      const targetAmount = await capitalRecoveryTracker.TARGET_AMOUNT();
      const payment = ethers.parseEther("30000");

      await capitalRecoveryTracker.recordPayment(payment);

      const remaining = await capitalRecoveryTracker.remaining();
      expect(remaining).to.equal(targetAmount - payment);
    });
  });

  describe("TokenDistributor", function () {
    it("Should receive funds and track distributions", async function () {
      const { tokenDistributor, owner } = await loadFixture(deployProfitSharingFixture);
      
      const amount = ethers.parseEther("5");
      
      // Send funds to distributor
      await owner.sendTransaction({
        to: await tokenDistributor.getAddress(),
        value: amount
      });
      
      expect(await ethers.provider.getBalance(await tokenDistributor.getAddress())).to.equal(amount);
    });

    it("Should distribute funds correctly", async function () {
      const { tokenDistributor, profitToken, owner, investor1 } = await loadFixture(deployProfitSharingFixture);
      
      // Set up KYC and transfer tokens
      await profitToken.updateKYCStatus(owner.address, true);
      await profitToken.updateKYCStatus(investor1.address, true);
      
      const tokenAmount = ethers.parseEther("1000");
      await profitToken.transfer(investor1.address, tokenAmount);
      
      // Send funds to distributor
      const distributionAmount = ethers.parseEther("2");
      await owner.sendTransaction({
        to: await tokenDistributor.getAddress(),
        value: distributionAmount
      });
      
      // Execute distribution
      await tokenDistributor.distribute();
      
      expect(await tokenDistributor.distributionCounter()).to.equal(1);
    });
  });

  describe("ProfitSplitter Integration", function () {
    it("Should distribute profits correctly during capital recovery", async function () {
      const { profitSplitter, capitalPool, owner } = await loadFixture(deployProfitSharingFixture);
      
      const profitAmount = ethers.parseEther("10");
      
      // Send profit to splitter
      await owner.sendTransaction({
        to: await profitSplitter.getAddress(),
        value: profitAmount
      });
      
      const initialBalance = await ethers.provider.getBalance(capitalPool.address);
      
      // Distribute profit
      await profitSplitter.distributeProfit();
      
      const finalBalance = await ethers.provider.getBalance(capitalPool.address);
      expect(finalBalance - initialBalance).to.equal(profitAmount);
    });

    it("Should split profits correctly after capital recovery", async function () {
      const { profitSplitter, capitalRecoveryTracker, capitalPool, retainedShare, owner } = await loadFixture(deployProfitSharingFixture);

      // Get the actual target amount and send enough to complete capital recovery
      const targetAmount = await capitalRecoveryTracker.TARGET_AMOUNT();
      console.log("Target amount:", ethers.formatEther(targetAmount), "ETH");

      // Send enough profit to complete capital recovery (send the full target amount)
      await owner.sendTransaction({
        to: await profitSplitter.getAddress(),
        value: targetAmount
      });

      // Distribute profit to complete capital recovery
      await profitSplitter.distributeProfit();

      // Verify we're in post-recovery stage
      expect(await capitalRecoveryTracker.getStage()).to.equal(1);

      const profitAmount = ethers.parseEther("10");

      // Send profit to splitter
      await owner.sendTransaction({
        to: await profitSplitter.getAddress(),
        value: profitAmount
      });

      const initialCapitalBalance = await ethers.provider.getBalance(capitalPool.address);
      const initialRetainedBalance = await ethers.provider.getBalance(retainedShare.address);

      // Distribute profit
      await profitSplitter.distributeProfit();

      const finalCapitalBalance = await ethers.provider.getBalance(capitalPool.address);
      const finalRetainedBalance = await ethers.provider.getBalance(retainedShare.address);

      // In post-recovery, capital pool should get nothing, retained share should get 50%
      expect(finalCapitalBalance).to.equal(initialCapitalBalance);
      expect(finalRetainedBalance - initialRetainedBalance).to.equal(profitAmount / 2n);
    }).timeout(10000);
  });
});
