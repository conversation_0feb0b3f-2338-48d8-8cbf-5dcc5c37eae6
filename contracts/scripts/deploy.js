const { ethers } = require("hardhat");

async function main() {
  console.log("Starting deployment of Archimedes Finance Profit Sharing contracts...");
  
  // Get the deployer account
  const [deployer] = await ethers.getSigners();
  console.log("Deploying contracts with account:", deployer.address);
  console.log("Account balance:", (await ethers.provider.getBalance(deployer.address)).toString());

  // Deploy ProfitToken first
  console.log("\n1. Deploying ProfitToken...");
  const ProfitToken = await ethers.getContractFactory("ProfitToken");
  const profitToken = await ProfitToken.deploy();
  await profitToken.waitForDeployment();
  console.log("ProfitToken deployed to:", await profitToken.getAddress());

  // Deploy CapitalRecoveryTracker
  console.log("\n2. Deploying CapitalRecoveryTracker...");
  const CapitalRecoveryTracker = await ethers.getContractFactory("CapitalRecoveryTracker");
  const capitalRecoveryTracker = await CapitalRecoveryTracker.deploy(0); // Use default 150k CAD
  await capitalRecoveryTracker.waitForDeployment();
  console.log("CapitalRecoveryTracker deployed to:", await capitalRecoveryTracker.getAddress());

  // Deploy TokenDistributor
  console.log("\n3. Deploying TokenDistributor...");
  const TokenDistributor = await ethers.getContractFactory("TokenDistributor");
  const tokenDistributor = await TokenDistributor.deploy(await profitToken.getAddress());
  await tokenDistributor.waitForDeployment();
  console.log("TokenDistributor deployed to:", await tokenDistributor.getAddress());

  // For demo purposes, use deployer address as capital pool and retained share addresses
  const capitalPoolAddress = deployer.address;
  const retainedShareAddress = deployer.address;

  // Deploy ProfitSplitter
  console.log("\n4. Deploying ProfitSplitter...");
  const ProfitSplitter = await ethers.getContractFactory("ProfitSplitter");
  const profitSplitter = await ProfitSplitter.deploy(
    await capitalRecoveryTracker.getAddress(),
    await tokenDistributor.getAddress(),
    capitalPoolAddress,
    retainedShareAddress
  );
  await profitSplitter.waitForDeployment();
  console.log("ProfitSplitter deployed to:", await profitSplitter.getAddress());

  // Transfer ownership of CapitalRecoveryTracker to ProfitSplitter
  console.log("\n5. Transferring ownership of CapitalRecoveryTracker to ProfitSplitter...");
  await capitalRecoveryTracker.transferOwnership(await profitSplitter.getAddress());
  console.log("Ownership transferred successfully");

  // Verify deployment by checking contract states
  console.log("\n6. Verifying deployments...");
  
  // Check ProfitToken
  const tokenName = await profitToken.name();
  const tokenSymbol = await profitToken.symbol();
  const totalSupply = await profitToken.totalSupply();
  console.log(`ProfitToken: ${tokenName} (${tokenSymbol}), Total Supply: ${ethers.formatEther(totalSupply)} tokens`);

  // Check CapitalRecoveryTracker
  const currentStage = await capitalRecoveryTracker.getStage();
  const targetAmount = await capitalRecoveryTracker.TARGET_AMOUNT();
  const remaining = await capitalRecoveryTracker.remaining();
  console.log(`CapitalRecoveryTracker: Stage ${currentStage}, Target: ${ethers.formatEther(targetAmount)} CAD, Remaining: ${ethers.formatEther(remaining)} CAD`);

  // Check TokenDistributor
  const distributorToken = await tokenDistributor.profitToken();
  console.log(`TokenDistributor: Connected to token at ${distributorToken}`);

  // Check ProfitSplitter
  const splitterCapitalTracker = await profitSplitter.capitalRecoveryTracker();
  const splitterTokenDistributor = await profitSplitter.tokenDistributor();
  console.log(`ProfitSplitter: Connected to CapitalRecoveryTracker at ${splitterCapitalTracker}`);
  console.log(`ProfitSplitter: Connected to TokenDistributor at ${splitterTokenDistributor}`);

  console.log("\n✅ All contracts deployed successfully!");
  
  // Save deployment addresses to a file
  const deploymentInfo = {
    network: await ethers.provider.getNetwork(),
    deployer: deployer.address,
    contracts: {
      ProfitToken: await profitToken.getAddress(),
      CapitalRecoveryTracker: await capitalRecoveryTracker.getAddress(),
      TokenDistributor: await tokenDistributor.getAddress(),
      ProfitSplitter: await profitSplitter.getAddress()
    },
    deploymentTime: new Date().toISOString()
  };

  const fs = require('fs');
  fs.writeFileSync(
    'deployment-addresses.json',
    JSON.stringify(deploymentInfo, null, 2)
  );
  console.log("\n📄 Deployment addresses saved to deployment-addresses.json");

  return deploymentInfo;
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("Deployment failed:", error);
    process.exit(1);
  });
