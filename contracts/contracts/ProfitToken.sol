// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";

/**
 * @title ProfitToken
 * @dev ERC20 token representing profit sharing rights in Archimedes Finance
 */
contract ProfitToken is ERC20, Ownable {
    uint256 public constant MAX_SUPPLY = 1000000 * 10**18; // 1 million tokens max
    
    mapping(address => bool) public kycVerified;
    mapping(address => uint256) public vestingStart;
    mapping(address => uint256) public vestingDuration;
    
    event KYCStatusUpdated(address indexed investor, bool verified);
    event TokensVested(address indexed investor, uint256 amount, uint256 vestingStart, uint256 vestingDuration);
    
    modifier onlyKYCVerified(address account) {
        require(kycVerified[account], "Account not KYC verified");
        _;
    }
    
    constructor() ERC20("Archimedes Profit Token", "APT") Ownable(msg.sender) {
        // Initial supply can be minted to owner for distribution
        _mint(msg.sender, 100000 * 10**18); // 100k initial tokens
    }
    
    /**
     * @dev Mint new tokens (only owner, up to max supply)
     */
    function mint(address to, uint256 amount) external onlyOwner {
        require(totalSupply() + amount <= MAX_SUPPLY, "Would exceed max supply");
        require(kycVerified[to], "Recipient not KYC verified");
        _mint(to, amount);
    }
    
    /**
     * @dev Update KYC status for an investor
     */
    function updateKYCStatus(address investor, bool verified) external onlyOwner {
        kycVerified[investor] = verified;
        emit KYCStatusUpdated(investor, verified);
    }
    
    /**
     * @dev Set up vesting for tokens
     */
    function setupVesting(address investor, uint256 amount, uint256 duration) external onlyOwner {
        require(kycVerified[investor], "Investor not KYC verified");
        require(balanceOf(investor) >= amount, "Insufficient balance for vesting");
        
        vestingStart[investor] = block.timestamp;
        vestingDuration[investor] = duration;
        
        emit TokensVested(investor, amount, block.timestamp, duration);
    }
    
    /**
     * @dev Calculate vested amount for an investor
     */
    function getVestedAmount(address investor) public view returns (uint256) {
        if (vestingStart[investor] == 0 || vestingDuration[investor] == 0) {
            return balanceOf(investor); // No vesting, all tokens available
        }
        
        uint256 elapsed = block.timestamp - vestingStart[investor];
        if (elapsed >= vestingDuration[investor]) {
            return balanceOf(investor); // Fully vested
        }
        
        // Linear vesting
        return (balanceOf(investor) * elapsed) / vestingDuration[investor];
    }
    
    /**
     * @dev Override transfer to check KYC and vesting
     */
    function transfer(address to, uint256 amount) public override onlyKYCVerified(msg.sender) onlyKYCVerified(to) returns (bool) {
        uint256 vestedAmount = getVestedAmount(msg.sender);
        require(amount <= vestedAmount, "Transfer amount exceeds vested tokens");
        return super.transfer(to, amount);
    }
    
    /**
     * @dev Override transferFrom to check KYC and vesting
     */
    function transferFrom(address from, address to, uint256 amount) public override onlyKYCVerified(from) onlyKYCVerified(to) returns (bool) {
        uint256 vestedAmount = getVestedAmount(from);
        require(amount <= vestedAmount, "Transfer amount exceeds vested tokens");
        return super.transferFrom(from, to, amount);
    }
    
    /**
     * @dev Burn tokens (only owner)
     */
    function burn(uint256 amount) external onlyOwner {
        _burn(msg.sender, amount);
    }
    
    /**
     * @dev Burn tokens from a specific account (only owner)
     */
    function burnFrom(address account, uint256 amount) external onlyOwner {
        _burn(account, amount);
    }
}
