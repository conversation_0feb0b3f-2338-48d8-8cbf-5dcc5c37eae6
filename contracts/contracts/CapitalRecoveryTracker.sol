// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";

/**
 * @title CapitalRecoveryTracker
 * @dev Tracks the CAD 150k capital pool repayment and manages stage transitions
 */
contract CapitalRecoveryTracker is Ownable, ReentrancyGuard {
    uint256 public TARGET_AMOUNT; // Target amount in wei equivalent
    uint256 public cumulativeRepaid;
    
    enum Stage {
        CapitalRecovery,
        PostRecovery
    }
    
    Stage public currentStage;
    
    event PaymentRecorded(uint256 amount, uint256 cumulativeTotal, address indexed recorder);
    event StageTransition(Stage previousStage, Stage newStage, uint256 timestamp);
    
    constructor(uint256 _targetAmount) Ownable(msg.sender) {
        TARGET_AMOUNT = _targetAmount > 0 ? _targetAmount : 150000 * 10**18; // Default to 150k CAD
        currentStage = Stage.CapitalRecovery;
        cumulativeRepaid = 0;
    }
    
    /**
     * @dev Records a payment towards capital recovery
     * @param amount The amount being paid (in wei equivalent)
     */
    function recordPayment(uint256 amount) external onlyOwner nonReentrant {
        require(amount > 0, "Payment amount must be greater than zero");
        
        uint256 previousTotal = cumulativeRepaid;
        cumulativeRepaid += amount;
        
        emit PaymentRecorded(amount, cumulativeRepaid, msg.sender);
        
        // Check if we've reached the target and need to transition stages
        if (previousTotal < TARGET_AMOUNT && cumulativeRepaid >= TARGET_AMOUNT) {
            Stage previousStage = currentStage;
            currentStage = Stage.PostRecovery;
            emit StageTransition(previousStage, currentStage, block.timestamp);
        }
    }
    
    /**
     * @dev Returns the current stage
     */
    function getStage() external view returns (Stage) {
        return currentStage;
    }
    
    /**
     * @dev Returns the remaining amount to reach target
     */
    function remaining() external view returns (uint256) {
        if (cumulativeRepaid >= TARGET_AMOUNT) {
            return 0;
        }
        return TARGET_AMOUNT - cumulativeRepaid;
    }
    
    /**
     * @dev Returns the percentage of capital recovered
     */
    function getRecoveryPercentage() external view returns (uint256) {
        if (cumulativeRepaid >= TARGET_AMOUNT) {
            return 100;
        }
        return (cumulativeRepaid * 100) / TARGET_AMOUNT;
    }
    
    /**
     * @dev Emergency function to reset cumulative repaid (only owner)
     */
    function resetCumulativeRepaid() external onlyOwner {
        cumulativeRepaid = 0;
        currentStage = Stage.CapitalRecovery;
        emit StageTransition(Stage.PostRecovery, Stage.CapitalRecovery, block.timestamp);
    }
}
