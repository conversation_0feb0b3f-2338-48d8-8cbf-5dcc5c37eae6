// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "./CapitalRecoveryTracker.sol";
import "./TokenDistributor.sol";

/**
 * @title ProfitSplitter
 * @dev Accepts Plains North's monthly 1/3 profit share and routes funds based on current stage
 */
contract ProfitSplitter is Ownable, ReentrancyGuard {
    CapitalRecoveryTracker public capitalRecoveryTracker;
    TokenDistributor public tokenDistributor;
    
    address public capitalPoolAddress;
    address public retainedShareAddress;
    
    uint256 public totalProfitsReceived;
    uint256 public totalToCapitalPool;
    uint256 public totalToRetainedShare;
    uint256 public totalToTokenisedPool;
    
    struct Distribution {
        uint256 totalAmount;
        uint256 toCapitalPool;
        uint256 toRetainedShare;
        uint256 toTokenisedPool;
        uint256 timestamp;
        CapitalRecoveryTracker.Stage stage;
    }
    
    Distribution[] public distributionHistory;
    
    event ProfitReceived(uint256 amount, address indexed sender);
    event ProfitDistributed(
        uint256 distributionId,
        uint256 totalAmount,
        uint256 toCapitalPool,
        uint256 toRetainedShare,
        uint256 toTokenisedPool,
        CapitalRecoveryTracker.Stage stage
    );
    
    constructor(
        address _capitalRecoveryTracker,
        address payable _tokenDistributor,
        address _capitalPoolAddress,
        address _retainedShareAddress
    ) Ownable(msg.sender) {
        require(_capitalRecoveryTracker != address(0), "Invalid capital recovery tracker");
        require(_tokenDistributor != address(0), "Invalid token distributor");
        require(_capitalPoolAddress != address(0), "Invalid capital pool address");
        require(_retainedShareAddress != address(0), "Invalid retained share address");

        capitalRecoveryTracker = CapitalRecoveryTracker(_capitalRecoveryTracker);
        tokenDistributor = TokenDistributor(_tokenDistributor);
        capitalPoolAddress = _capitalPoolAddress;
        retainedShareAddress = _retainedShareAddress;
    }
    
    /**
     * @dev Receive ETH profits
     */
    receive() external payable {
        emit ProfitReceived(msg.value, msg.sender);
        totalProfitsReceived += msg.value;
    }
    
    /**
     * @dev Distribute profits based on current stage
     */
    function distributeProfit() external onlyOwner nonReentrant {
        uint256 availableBalance = address(this).balance;
        require(availableBalance > 0, "No profits to distribute");
        
        CapitalRecoveryTracker.Stage currentStage = capitalRecoveryTracker.getStage();
        
        uint256 toCapitalPool = 0;
        uint256 toRetainedShare = 0;
        uint256 toTokenisedPool = 0;
        
        if (currentStage == CapitalRecoveryTracker.Stage.CapitalRecovery) {
            // During capital recovery: 100% goes to capital pool
            toCapitalPool = availableBalance;
        } else {
            // Post-recovery: 50% to retained share, 50% to tokenised profit pool
            toRetainedShare = availableBalance / 2;
            toTokenisedPool = availableBalance - toRetainedShare;
        }
        
        // Record the distribution
        distributionHistory.push(Distribution({
            totalAmount: availableBalance,
            toCapitalPool: toCapitalPool,
            toRetainedShare: toRetainedShare,
            toTokenisedPool: toTokenisedPool,
            timestamp: block.timestamp,
            stage: currentStage
        }));
        
        uint256 distributionId = distributionHistory.length - 1;
        
        // Execute transfers
        if (toCapitalPool > 0) {
            totalToCapitalPool += toCapitalPool;
            (bool success, ) = payable(capitalPoolAddress).call{value: toCapitalPool}("");
            require(success, "Transfer to capital pool failed");
            
            // Record payment in capital recovery tracker
            capitalRecoveryTracker.recordPayment(toCapitalPool);
        }
        
        if (toRetainedShare > 0) {
            totalToRetainedShare += toRetainedShare;
            (bool success, ) = payable(retainedShareAddress).call{value: toRetainedShare}("");
            require(success, "Transfer to retained share failed");
        }
        
        if (toTokenisedPool > 0) {
            totalToTokenisedPool += toTokenisedPool;
            (bool success, ) = payable(address(tokenDistributor)).call{value: toTokenisedPool}("");
            require(success, "Transfer to tokenised pool failed");
        }
        
        emit ProfitDistributed(
            distributionId,
            availableBalance,
            toCapitalPool,
            toRetainedShare,
            toTokenisedPool,
            currentStage
        );
    }
    
    /**
     * @dev Get distribution history
     */
    function getDistributionHistory() external view returns (Distribution[] memory) {
        return distributionHistory;
    }
    
    /**
     * @dev Get the latest distribution
     */
    function getLatestDistribution() external view returns (Distribution memory) {
        require(distributionHistory.length > 0, "No distributions yet");
        return distributionHistory[distributionHistory.length - 1];
    }
    
    /**
     * @dev Update contract addresses (only owner)
     */
    function updateAddresses(
        address _capitalRecoveryTracker,
        address payable _tokenDistributor,
        address _capitalPoolAddress,
        address _retainedShareAddress
    ) external onlyOwner {
        if (_capitalRecoveryTracker != address(0)) {
            capitalRecoveryTracker = CapitalRecoveryTracker(_capitalRecoveryTracker);
        }
        if (_tokenDistributor != address(0)) {
            tokenDistributor = TokenDistributor(_tokenDistributor);
        }
        if (_capitalPoolAddress != address(0)) {
            capitalPoolAddress = _capitalPoolAddress;
        }
        if (_retainedShareAddress != address(0)) {
            retainedShareAddress = _retainedShareAddress;
        }
    }
    
    /**
     * @dev Emergency withdrawal (only owner)
     */
    function emergencyWithdraw() external onlyOwner {
        uint256 balance = address(this).balance;
        require(balance > 0, "No funds to withdraw");
        
        (bool success, ) = payable(owner()).call{value: balance}("");
        require(success, "Emergency withdrawal failed");
    }
}
