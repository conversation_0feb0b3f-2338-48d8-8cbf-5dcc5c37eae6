// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";

/**
 * @title TokenDistributor
 * @dev Holds the tokenised profit pool and distributes funds to token holders pro-rata
 */
contract TokenDistributor is Ownable, ReentrancyGuard {
    IERC20 public profitToken;
    uint256 public totalDistributed;
    
    struct PayoutRecord {
        uint256 amount;
        uint256 timestamp;
        uint256 distributionId;
    }
    
    mapping(address => PayoutRecord[]) public payoutHistory;
    mapping(uint256 => uint256) public distributionAmounts;
    uint256 public distributionCounter;
    
    event FundsReceived(uint256 amount, address indexed sender);
    event DistributionExecuted(uint256 distributionId, uint256 totalAmount, uint256 timestamp);
    event PayoutSent(address indexed investor, uint256 amount, uint256 distributionId);
    
    constructor(address _profitToken) Ownable(msg.sender) {
        require(_profitToken != address(0), "Invalid token address");
        profitToken = IERC20(_profitToken);
        distributionCounter = 0;
    }
    
    /**
     * @dev Receive ETH for distribution
     */
    receive() external payable {
        emit FundsReceived(msg.value, msg.sender);
    }
    
    /**
     * @dev Distribute funds to all token holders pro-rata based on their token balances
     */
    function distribute() external onlyOwner nonReentrant {
        uint256 availableBalance = address(this).balance;
        require(availableBalance > 0, "No funds available for distribution");
        
        uint256 totalSupply = profitToken.totalSupply();
        require(totalSupply > 0, "No tokens in circulation");
        
        distributionCounter++;
        distributionAmounts[distributionCounter] = availableBalance;
        totalDistributed += availableBalance;
        
        emit DistributionExecuted(distributionCounter, availableBalance, block.timestamp);
        
        // Note: In a real implementation, you would need to iterate through token holders
        // This is a simplified version that requires external calls to distributeToInvestor
    }
    
    /**
     * @dev Distribute funds to a specific investor based on their token balance
     * @param investor The address of the investor
     */
    function distributeToInvestor(address investor) external onlyOwner nonReentrant {
        require(investor != address(0), "Invalid investor address");
        require(distributionCounter > 0, "No distributions available");
        
        uint256 investorBalance = profitToken.balanceOf(investor);
        require(investorBalance > 0, "Investor has no tokens");
        
        uint256 totalSupply = profitToken.totalSupply();
        uint256 distributionAmount = distributionAmounts[distributionCounter];
        
        uint256 investorShare = (distributionAmount * investorBalance) / totalSupply;
        require(investorShare > 0, "No share available for investor");
        require(address(this).balance >= investorShare, "Insufficient contract balance");
        
        // Record the payout
        payoutHistory[investor].push(PayoutRecord({
            amount: investorShare,
            timestamp: block.timestamp,
            distributionId: distributionCounter
        }));
        
        // Send the funds
        (bool success, ) = payable(investor).call{value: investorShare}("");
        require(success, "Transfer failed");
        
        emit PayoutSent(investor, investorShare, distributionCounter);
    }
    
    /**
     * @dev Get payout history for an investor
     * @param investor The address of the investor
     */
    function getPayoutHistory(address investor) external view returns (PayoutRecord[] memory) {
        return payoutHistory[investor];
    }
    
    /**
     * @dev Get the total amount an investor has received
     * @param investor The address of the investor
     */
    function getTotalPayouts(address investor) external view returns (uint256) {
        uint256 total = 0;
        PayoutRecord[] memory history = payoutHistory[investor];
        for (uint256 i = 0; i < history.length; i++) {
            total += history[i].amount;
        }
        return total;
    }
    
    /**
     * @dev Calculate potential payout for an investor based on current balance
     * @param investor The address of the investor
     */
    function calculatePotentialPayout(address investor) external view returns (uint256) {
        uint256 investorBalance = profitToken.balanceOf(investor);
        if (investorBalance == 0) return 0;
        
        uint256 totalSupply = profitToken.totalSupply();
        if (totalSupply == 0) return 0;
        
        uint256 availableBalance = address(this).balance;
        return (availableBalance * investorBalance) / totalSupply;
    }
    
    /**
     * @dev Emergency withdrawal function (only owner)
     */
    function emergencyWithdraw() external onlyOwner {
        uint256 balance = address(this).balance;
        require(balance > 0, "No funds to withdraw");
        
        (bool success, ) = payable(owner()).call{value: balance}("");
        require(success, "Emergency withdrawal failed");
    }
    
    /**
     * @dev Update the profit token address (only owner)
     */
    function updateProfitToken(address _newToken) external onlyOwner {
        require(_newToken != address(0), "Invalid token address");
        profitToken = IERC20(_newToken);
    }
}
