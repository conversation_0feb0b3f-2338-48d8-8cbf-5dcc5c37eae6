import { describe, it, expect, beforeEach, vi } from 'vitest';
import { setActivePinia, createPinia } from 'pinia';
import { useProfitSharingStore } from '@/stores/profitSharing';
import { profitSharingApi } from '@/api/profitSharing';

// Mock the API
vi.mock('@/api/profitSharing', () => ({
  profitSharingApi: {
    getCapitalStatus: vi.fn(),
    updateCapitalRecovery: vi.fn(),
    getTokenBalance: vi.fn(),
    getPayoutHistory: vi.fn(),
    submitProfit: vi.fn(),
    approveProfitSubmission: vi.fn(),
    getDistributions: vi.fn(),
    getInvestorDashboard: vi.fn(),
    getAdminDashboard: vi.fn(),
    getStatistics: vi.fn(),
    syncTokenHoldings: vi.fn(),
    updateKycStatus: vi.fn()
  }
}));

describe('Profit Sharing Store', () => {
  let store;

  beforeEach(() => {
    setActivePinia(createPinia());
    store = useProfitSharingStore();
    vi.clearAllMocks();
  });

  describe('Initial State', () => {
    it('has correct initial state', () => {
      expect(store.capitalStatus).toBeNull();
      expect(store.tokenHoldings).toEqual([]);
      expect(store.payoutHistory).toEqual([]);
      expect(store.distributions).toEqual([]);
      expect(store.profitSubmissions).toEqual([]);
      expect(store.statistics).toEqual({});
      expect(store.loading).toBe(false);
      expect(store.error).toBeNull();
    });
  });

  describe('Getters', () => {
    beforeEach(() => {
      store.capitalStatus = {
        stage: 'capital_recovery',
        recovery_percentage: 66.67
      };
      
      store.tokenHoldings = [
        { balance: '100000000000000000000' }, // 100 tokens
        { balance: '50000000000000000000' }   // 50 tokens
      ];
      
      store.payoutHistory = [
        { amount: 1000 },
        { amount: 1500 }
      ];
    });

    it('calculates isCapitalRecoveryComplete correctly', () => {
      expect(store.isCapitalRecoveryComplete).toBe(false);
      
      store.capitalStatus.stage = 'post_recovery';
      expect(store.isCapitalRecoveryComplete).toBe(true);
    });

    it('calculates recoveryPercentage correctly', () => {
      expect(store.recoveryPercentage).toBe(66.67);
    });

    it('calculates totalTokenBalance correctly', () => {
      expect(store.totalTokenBalance).toBe(150000000000000000000);
    });

    it('calculates totalReceived correctly', () => {
      expect(store.totalReceived).toBe(2500);
    });

    it('filters pendingSubmissions correctly', () => {
      store.profitSubmissions = [
        { id: 1, status: 'pending' },
        { id: 2, status: 'approved' },
        { id: 3, status: 'pending' }
      ];
      
      expect(store.pendingSubmissions).toHaveLength(2);
      expect(store.pendingSubmissions[0].id).toBe(1);
      expect(store.pendingSubmissions[1].id).toBe(3);
    });

    it('returns recent distributions correctly', () => {
      const distributions = Array.from({ length: 15 }, (_, i) => ({ id: i + 1 }));
      store.distributions = distributions;
      
      expect(store.recentDistributions).toHaveLength(10);
      expect(store.recentDistributions[0].id).toBe(1);
    });
  });

  describe('Capital Recovery Actions', () => {
    it('fetches capital status successfully', async () => {
      const mockData = {
        target_amount: 150000,
        repaid_amount: 50000,
        stage: 'capital_recovery'
      };
      
      profitSharingApi.getCapitalStatus.mockResolvedValue({
        data: mockData
      });

      const result = await store.fetchCapitalStatus();

      expect(profitSharingApi.getCapitalStatus).toHaveBeenCalled();
      expect(store.capitalStatus).toEqual(mockData);
      expect(result).toEqual(mockData);
      expect(store.loading).toBe(false);
      expect(store.error).toBeNull();
    });

    it('handles capital status fetch error', async () => {
      const errorMessage = 'Failed to fetch capital status';
      profitSharingApi.getCapitalStatus.mockRejectedValue(new Error(errorMessage));

      await expect(store.fetchCapitalStatus()).rejects.toThrow(errorMessage);
      expect(store.error).toBe(errorMessage);
      expect(store.loading).toBe(false);
    });

    it('updates capital recovery successfully', async () => {
      const mockResponse = {
        data: {
          success: true,
          capitalStatus: { repaid_amount: 75000 }
        }
      };
      
      profitSharingApi.updateCapitalRecovery.mockResolvedValue(mockResponse);
      profitSharingApi.getCapitalStatus.mockResolvedValue({
        data: { repaid_amount: 75000 }
      });

      const result = await store.updateCapitalRecovery(25000, '0xabc123');

      expect(profitSharingApi.updateCapitalRecovery).toHaveBeenCalledWith(25000, '0xabc123');
      expect(profitSharingApi.getCapitalStatus).toHaveBeenCalled();
      expect(result).toEqual(mockResponse.data);
    });
  });

  describe('Token Holdings Actions', () => {
    it('fetches token holdings successfully', async () => {
      const mockHoldings = [
        {
          id: 1,
          balance: '100000000000000000000',
          token: { token_symbol: 'TPT' }
        }
      ];
      
      profitSharingApi.getTokenBalance.mockResolvedValue({
        data: mockHoldings
      });

      const result = await store.fetchTokenHoldings(123);

      expect(profitSharingApi.getTokenBalance).toHaveBeenCalledWith(123);
      expect(store.tokenHoldings).toEqual(mockHoldings);
      expect(result).toEqual(mockHoldings);
    });

    it('syncs token holdings successfully', async () => {
      const mockResponse = {
        data: { syncedCount: 5, totalUsers: 10 }
      };
      
      profitSharingApi.syncTokenHoldings.mockResolvedValue(mockResponse);

      const result = await store.syncTokenHoldings();

      expect(profitSharingApi.syncTokenHoldings).toHaveBeenCalled();
      expect(result).toEqual(mockResponse.data);
    });
  });

  describe('Profit Submission Actions', () => {
    it('submits profit successfully', async () => {
      const profitData = {
        gross_profit: 300000,
        net_profit: 150000,
        submission_period: '2024-01'
      };
      
      const mockResponse = {
        data: {
          submission: { id: 1, ...profitData },
          plains_north_share: 50000
        }
      };
      
      profitSharingApi.submitProfit.mockResolvedValue(mockResponse);
      profitSharingApi.getProfitSubmissions = vi.fn().mockResolvedValue({
        data: [mockResponse.data.submission]
      });

      const result = await store.submitProfit(profitData);

      expect(profitSharingApi.submitProfit).toHaveBeenCalledWith(profitData);
      expect(result).toEqual(mockResponse.data);
    });

    it('approves profit submission successfully', async () => {
      const mockResponse = {
        data: {
          submission: { id: 1, status: 'approved' },
          distribution: { id: 1, total_amount: 50000 }
        }
      };
      
      profitSharingApi.approveProfitSubmission.mockResolvedValue(mockResponse);
      profitSharingApi.getProfitSubmissions = vi.fn().mockResolvedValue({ data: [] });
      profitSharingApi.getDistributions.mockResolvedValue({ data: [] });
      profitSharingApi.getCapitalStatus.mockResolvedValue({ data: {} });

      const result = await store.approveProfitSubmission(1);

      expect(profitSharingApi.approveProfitSubmission).toHaveBeenCalledWith(1);
      expect(result).toEqual(mockResponse.data);
    });
  });

  describe('Dashboard Actions', () => {
    it('fetches investor dashboard successfully', async () => {
      const mockDashboard = {
        capital_status: { stage: 'capital_recovery' },
        token_balances: [{ balance: '100000000000000000000' }],
        recent_payouts: [{ amount: 1000 }],
        total_received: 1000
      };
      
      profitSharingApi.getInvestorDashboard.mockResolvedValue({
        data: mockDashboard
      });

      const result = await store.fetchInvestorDashboard(123);

      expect(profitSharingApi.getInvestorDashboard).toHaveBeenCalledWith(123);
      expect(store.capitalStatus).toEqual(mockDashboard.capital_status);
      expect(store.tokenHoldings).toEqual(mockDashboard.token_balances);
      expect(store.payoutHistory).toEqual(mockDashboard.recent_payouts);
      expect(result).toEqual(mockDashboard);
    });

    it('fetches admin dashboard successfully', async () => {
      const mockDashboard = {
        capital_status: { stage: 'capital_recovery' },
        recent_distributions: [{ id: 1 }],
        recent_profit_submissions: [{ id: 1 }],
        statistics: { total_distributed: 100000 }
      };
      
      profitSharingApi.getAdminDashboard.mockResolvedValue({
        data: mockDashboard
      });

      const result = await store.fetchAdminDashboard();

      expect(profitSharingApi.getAdminDashboard).toHaveBeenCalled();
      expect(store.capitalStatus).toEqual(mockDashboard.capital_status);
      expect(store.distributions).toEqual(mockDashboard.recent_distributions);
      expect(store.profitSubmissions).toEqual(mockDashboard.recent_profit_submissions);
      expect(store.statistics).toEqual(mockDashboard.statistics);
      expect(result).toEqual(mockDashboard);
    });
  });

  describe('Error Handling', () => {
    it('sets error state on API failure', async () => {
      const errorMessage = 'Network error';
      profitSharingApi.getCapitalStatus.mockRejectedValue(new Error(errorMessage));

      await expect(store.fetchCapitalStatus()).rejects.toThrow(errorMessage);
      expect(store.error).toBe(errorMessage);
      expect(store.loading).toBe(false);
    });

    it('clears error state', () => {
      store.error = 'Some error';
      store.clearError();
      expect(store.error).toBeNull();
    });
  });

  describe('Loading State Management', () => {
    it('sets loading state during async operations', async () => {
      profitSharingApi.getCapitalStatus.mockImplementation(() => {
        expect(store.loading).toBe(true);
        return Promise.resolve({ data: {} });
      });

      await store.fetchCapitalStatus();
      expect(store.loading).toBe(false);
    });

    it('resets loading state on error', async () => {
      profitSharingApi.getCapitalStatus.mockRejectedValue(new Error('Test error'));

      await expect(store.fetchCapitalStatus()).rejects.toThrow();
      expect(store.loading).toBe(false);
    });
  });

  describe('Utility Functions', () => {
    it('exports data to CSV format', () => {
      const testData = [
        { id: 1, name: 'Test 1', amount: 1000 },
        { id: 2, name: 'Test 2', amount: 2000 }
      ];

      // Mock DOM methods
      const mockCreateElement = vi.fn(() => ({
        setAttribute: vi.fn(),
        click: vi.fn(),
        style: {}
      }));
      const mockAppendChild = vi.fn();
      const mockRemoveChild = vi.fn();
      
      global.document = {
        createElement: mockCreateElement,
        body: {
          appendChild: mockAppendChild,
          removeChild: mockRemoveChild
        }
      };

      global.URL = {
        createObjectURL: vi.fn(() => 'blob:url')
      };

      global.Blob = vi.fn();

      store.exportData(testData, 'test-export');

      expect(mockCreateElement).toHaveBeenCalledWith('a');
      expect(mockAppendChild).toHaveBeenCalled();
      expect(mockRemoveChild).toHaveBeenCalled();
    });
  });

  describe('Store Reset', () => {
    it('resets store to initial state', () => {
      // Set some state
      store.capitalStatus = { stage: 'post_recovery' };
      store.tokenHoldings = [{ balance: '1000' }];
      store.loading = true;
      store.error = 'Some error';

      // Reset
      store.$reset();

      // Verify reset
      expect(store.capitalStatus).toBeNull();
      expect(store.tokenHoldings).toEqual([]);
      expect(store.loading).toBe(false);
      expect(store.error).toBeNull();
    });
  });
});
