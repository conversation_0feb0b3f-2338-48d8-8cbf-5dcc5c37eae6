import { describe, it, expect, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import KpiCard from '@/components/profit-sharing/KpiCard.vue';

describe('KpiCard Component', () => {
  let wrapper;

  const defaultProps = {
    title: 'Test KPI',
    value: 1234.56,
    unit: 'CAD',
    description: 'Test description'
  };

  beforeEach(() => {
    wrapper = mount(KpiCard, {
      props: defaultProps
    });
  });

  describe('Basic Rendering', () => {
    it('renders the component correctly', () => {
      expect(wrapper.exists()).toBe(true);
      expect(wrapper.find('.kpi-card').exists()).toBe(true);
    });

    it('displays the title correctly', () => {
      expect(wrapper.find('.kpi-title').text()).toBe('TEST KPI');
    });

    it('displays the description when provided', () => {
      expect(wrapper.find('.kpi-description').text()).toBe('Test description');
    });

    it('displays the unit when provided', () => {
      expect(wrapper.find('.value-unit').text()).toBe('CAD');
    });
  });

  describe('Value Formatting', () => {
    it('formats numbers correctly by default', () => {
      expect(wrapper.find('.value-main').text()).toBe('1.2K');
    });

    it('formats currency correctly', async () => {
      await wrapper.setProps({
        format: 'currency',
        currency: 'CAD',
        value: 1234.56
      });
      
      expect(wrapper.find('.value-main').text()).toBe('$1,235');
    });

    it('formats percentages correctly', async () => {
      await wrapper.setProps({
        format: 'percentage',
        value: 75.5
      });
      
      expect(wrapper.find('.value-main').text()).toBe('75.5%');
    });

    it('handles large numbers with abbreviations', async () => {
      await wrapper.setProps({
        value: 1500000
      });
      
      expect(wrapper.find('.value-main').text()).toBe('1.5M');
    });

    it('handles small numbers without abbreviations', async () => {
      await wrapper.setProps({
        value: 500
      });
      
      expect(wrapper.find('.value-main').text()).toBe('500');
    });
  });

  describe('Variants and Styling', () => {
    it('applies primary variant class', async () => {
      await wrapper.setProps({ variant: 'primary' });
      expect(wrapper.find('.kpi-card--primary').exists()).toBe(true);
    });

    it('applies success variant class', async () => {
      await wrapper.setProps({ variant: 'success' });
      expect(wrapper.find('.kpi-card--success').exists()).toBe(true);
    });

    it('applies correct size classes', async () => {
      await wrapper.setProps({ size: 'large' });
      expect(wrapper.find('.kpi-card--large').exists()).toBe(true);
      expect(wrapper.find('.value-main.h3').exists()).toBe(true);
    });

    it('applies small size classes', async () => {
      await wrapper.setProps({ size: 'small' });
      expect(wrapper.find('.kpi-card--small').exists()).toBe(true);
      expect(wrapper.find('.value-main.h6').exists()).toBe(true);
    });
  });

  describe('Icon Display', () => {
    it('displays icon when provided', async () => {
      await wrapper.setProps({
        icon: 'bi bi-cash',
        iconColor: '#28a745'
      });
      
      const icon = wrapper.find('.kpi-icon i');
      expect(icon.exists()).toBe(true);
      expect(icon.classes()).toContain('bi-cash');
      expect(icon.element.style.color).toBe('rgb(40, 167, 69)');
    });

    it('does not display icon when not provided', () => {
      expect(wrapper.find('.kpi-icon').exists()).toBe(false);
    });
  });

  describe('Trend Display', () => {
    it('displays positive trend correctly', async () => {
      await wrapper.setProps({ trend: 5.2 });
      
      const trendBadge = wrapper.find('.kpi-trend .badge');
      expect(trendBadge.exists()).toBe(true);
      expect(trendBadge.classes()).toContain('bg-success');
      expect(trendBadge.text()).toContain('5.2%');
      expect(wrapper.find('.bi-arrow-up').exists()).toBe(true);
    });

    it('displays negative trend correctly', async () => {
      await wrapper.setProps({ trend: -3.1 });
      
      const trendBadge = wrapper.find('.kpi-trend .badge');
      expect(trendBadge.classes()).toContain('bg-danger');
      expect(trendBadge.text()).toContain('3.1%');
      expect(wrapper.find('.bi-arrow-down').exists()).toBe(true);
    });

    it('displays zero trend correctly', async () => {
      await wrapper.setProps({ trend: 0 });
      
      const trendBadge = wrapper.find('.kpi-trend .badge');
      expect(trendBadge.classes()).toContain('bg-secondary');
      expect(wrapper.find('.bi-dash').exists()).toBe(true);
    });

    it('does not display trend when not provided', () => {
      expect(wrapper.find('.kpi-trend').exists()).toBe(false);
    });
  });

  describe('Loading State', () => {
    it('displays loading state correctly', async () => {
      await wrapper.setProps({ loading: true });
      
      expect(wrapper.find('.kpi-card--loading').exists()).toBe(true);
      expect(wrapper.find('.value-main').text()).toBe('...');
    });

    it('applies loading animation class', async () => {
      await wrapper.setProps({ loading: true });
      expect(wrapper.find('.kpi-card--loading').exists()).toBe(true);
    });
  });

  describe('Last Updated Display', () => {
    it('displays last updated time when provided', async () => {
      const lastUpdated = new Date('2024-01-15T10:30:00Z');
      await wrapper.setProps({ lastUpdated });
      
      const footer = wrapper.find('.card-footer');
      expect(footer.exists()).toBe(true);
      expect(footer.text()).toContain('Updated');
    });

    it('does not display footer when lastUpdated not provided', () => {
      expect(wrapper.find('.card-footer').exists()).toBe(false);
    });
  });

  describe('Slots', () => {
    it('renders footer slot content', () => {
      const wrapperWithSlot = mount(KpiCard, {
        props: defaultProps,
        slots: {
          footer: '<button class="btn btn-sm btn-primary">View Details</button>'
        }
      });
      
      expect(wrapperWithSlot.find('.kpi-footer button').exists()).toBe(true);
      expect(wrapperWithSlot.find('.kpi-footer button').text()).toBe('View Details');
    });
  });

  describe('Edge Cases', () => {
    it('handles null/undefined values gracefully', async () => {
      await wrapper.setProps({ value: null });
      expect(wrapper.find('.value-main').text()).toBe('null');
    });

    it('handles string values', async () => {
      await wrapper.setProps({ value: 'N/A' });
      expect(wrapper.find('.value-main').text()).toBe('N/A');
    });

    it('handles very large numbers', async () => {
      await wrapper.setProps({ value: 1234567890 });
      expect(wrapper.find('.value-main').text()).toBe('1234.6M');
    });

    it('handles negative numbers', async () => {
      await wrapper.setProps({ 
        value: -1500,
        format: 'currency'
      });
      expect(wrapper.find('.value-main').text()).toBe('-$1,500');
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA attributes', () => {
      const card = wrapper.find('.card');
      expect(card.exists()).toBe(true);
    });

    it('has readable text content', () => {
      const title = wrapper.find('.kpi-title');
      expect(title.text()).toBeTruthy();
      
      const value = wrapper.find('.value-main');
      expect(value.text()).toBeTruthy();
    });
  });

  describe('Responsive Behavior', () => {
    it('maintains structure on different screen sizes', () => {
      // This would require more sophisticated testing with viewport changes
      // For now, we just verify the responsive classes exist
      expect(wrapper.find('.card').exists()).toBe(true);
      expect(wrapper.find('.card-body').exists()).toBe(true);
    });
  });
});
