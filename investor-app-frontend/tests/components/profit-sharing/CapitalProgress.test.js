import { describe, it, expect, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import CapitalProgress from '@/components/profit-sharing/CapitalProgress.vue';

describe('CapitalProgress Component', () => {
  let wrapper;

  const defaultProps = {
    targetAmount: 150000,
    repaidAmount: 50000,
    remainingAmount: 100000,
    stage: 'capital_recovery'
  };

  beforeEach(() => {
    wrapper = mount(CapitalProgress, {
      props: defaultProps
    });
  });

  describe('Basic Rendering', () => {
    it('renders the component correctly', () => {
      expect(wrapper.exists()).toBe(true);
      expect(wrapper.find('.capital-progress').exists()).toBe(true);
    });

    it('displays the correct title', () => {
      expect(wrapper.find('.card-title').text()).toContain('Capital Recovery Progress');
    });

    it('displays the stage badge', () => {
      const badge = wrapper.find('.badge');
      expect(badge.exists()).toBe(true);
      expect(badge.text()).toBe('Capital Recovery');
    });
  });

  describe('Progress Calculation', () => {
    it('calculates progress percentage correctly', () => {
      const percentage = wrapper.find('.progress-percentage');
      expect(percentage.text()).toBe('33.3%');
    });

    it('sets progress bar width correctly', () => {
      const progressBar = wrapper.find('.progress-bar');
      expect(progressBar.element.style.width).toBe('33.333333333333336%');
    });

    it('handles 100% completion', async () => {
      await wrapper.setProps({
        repaidAmount: 150000,
        remainingAmount: 0
      });
      
      const percentage = wrapper.find('.progress-percentage');
      expect(percentage.text()).toBe('100.0%');
      
      const progressBar = wrapper.find('.progress-bar');
      expect(progressBar.element.style.width).toBe('100%');
    });

    it('handles zero progress', async () => {
      await wrapper.setProps({
        repaidAmount: 0,
        remainingAmount: 150000
      });
      
      const percentage = wrapper.find('.progress-percentage');
      expect(percentage.text()).toBe('0.0%');
    });
  });

  describe('Progress Bar Styling', () => {
    it('applies correct color class for low progress', async () => {
      await wrapper.setProps({
        repaidAmount: 30000 // 20%
      });
      
      const progressBar = wrapper.find('.progress-bar');
      expect(progressBar.classes()).toContain('bg-primary');
    });

    it('applies correct color class for medium progress', async () => {
      await wrapper.setProps({
        repaidAmount: 75000 // 50%
      });
      
      const progressBar = wrapper.find('.progress-bar');
      expect(progressBar.classes()).toContain('bg-warning');
    });

    it('applies correct color class for high progress', async () => {
      await wrapper.setProps({
        repaidAmount: 120000 // 80%
      });
      
      const progressBar = wrapper.find('.progress-bar');
      expect(progressBar.classes()).toContain('bg-info');
    });

    it('applies correct color class for completion', async () => {
      await wrapper.setProps({
        repaidAmount: 150000 // 100%
      });
      
      const progressBar = wrapper.find('.progress-bar');
      expect(progressBar.classes()).toContain('bg-success');
    });
  });

  describe('Amount Display', () => {
    it('displays repaid amount correctly', () => {
      const repaidDetail = wrapper.findAll('.detail-value')[0];
      expect(repaidDetail.text()).toBe('$50,000');
    });

    it('displays remaining amount correctly', () => {
      const remainingDetail = wrapper.findAll('.detail-value')[1];
      expect(remainingDetail.text()).toBe('$100,000');
    });

    it('displays target amount correctly', () => {
      const targetDetail = wrapper.findAll('.detail-value')[2];
      expect(targetDetail.text()).toBe('$150,000');
    });

    it('formats currency with different currency code', async () => {
      await wrapper.setProps({ currency: 'USD' });
      
      const repaidDetail = wrapper.findAll('.detail-value')[0];
      expect(repaidDetail.text()).toBe('$50,000'); // Still shows $ but with USD formatting
    });
  });

  describe('Stage Information', () => {
    it('displays capital recovery stage info correctly', () => {
      const alert = wrapper.find('.alert');
      expect(alert.classes()).toContain('alert-info');
      expect(alert.text()).toContain('Capital Recovery in Progress');
      expect(alert.text()).toContain('100% of Plains North\'s profit share goes toward capital recovery');
    });

    it('displays post-recovery stage info correctly', async () => {
      await wrapper.setProps({ stage: 'post_recovery' });
      
      const badge = wrapper.find('.badge');
      expect(badge.classes()).toContain('bg-success');
      expect(badge.text()).toBe('Post-Recovery');
      
      const alert = wrapper.find('.alert');
      expect(alert.classes()).toContain('alert-success');
      expect(alert.text()).toContain('Capital Recovery Complete');
      expect(alert.text()).toContain('50/50 between retained share and tokenised profit pool');
    });

    it('displays correct stage icon', () => {
      const icon = wrapper.find('.alert i');
      expect(icon.classes()).toContain('bi-arrow-repeat');
    });

    it('displays correct post-recovery icon', async () => {
      await wrapper.setProps({ stage: 'post_recovery' });
      
      const icon = wrapper.find('.alert i');
      expect(icon.classes()).toContain('bi-check-circle-fill');
    });
  });

  describe('Timeline Display', () => {
    const milestones = [
      {
        title: '25% Recovery',
        amount: 37500,
        completed: true,
        date: new Date('2024-01-15')
      },
      {
        title: '50% Recovery',
        amount: 75000,
        completed: false,
        date: null
      }
    ];

    it('displays timeline when showTimeline is true', async () => {
      await wrapper.setProps({
        showTimeline: true,
        milestones
      });
      
      expect(wrapper.find('.timeline-section').exists()).toBe(true);
      expect(wrapper.find('.timeline').exists()).toBe(true);
    });

    it('does not display timeline when showTimeline is false', () => {
      expect(wrapper.find('.timeline-section').exists()).toBe(false);
    });

    it('renders milestone items correctly', async () => {
      await wrapper.setProps({
        showTimeline: true,
        milestones
      });
      
      const timelineItems = wrapper.findAll('.timeline-item');
      expect(timelineItems).toHaveLength(2);
      
      expect(timelineItems[0].text()).toContain('25% Recovery');
      expect(timelineItems[0].text()).toContain('$37,500');
      expect(timelineItems[0].classes()).toContain('timeline-item--completed');
      
      expect(timelineItems[1].text()).toContain('50% Recovery');
      expect(timelineItems[1].text()).toContain('$75,000');
      expect(timelineItems[1].classes()).not.toContain('timeline-item--completed');
    });

    it('displays milestone completion icons correctly', async () => {
      await wrapper.setProps({
        showTimeline: true,
        milestones
      });
      
      const completedIcon = wrapper.findAll('.timeline-marker i')[0];
      expect(completedIcon.classes()).toContain('bi-check-circle-fill');
      
      const incompleteIcon = wrapper.findAll('.timeline-marker i')[1];
      expect(incompleteIcon.classes()).toContain('bi-circle');
    });
  });

  describe('Loading State', () => {
    it('displays loading state correctly', async () => {
      await wrapper.setProps({ loading: true });
      
      const amounts = wrapper.findAll('.detail-value');
      amounts.forEach(amount => {
        expect(amount.text()).toBe('...');
      });
    });

    it('applies loading animation', async () => {
      await wrapper.setProps({ loading: true });
      expect(wrapper.find('.capital-progress.loading').exists()).toBe(true);
    });
  });

  describe('Last Updated Display', () => {
    it('displays last updated time when provided', async () => {
      const lastUpdated = new Date('2024-01-15T10:30:00Z');
      await wrapper.setProps({ lastUpdated });
      
      const footer = wrapper.find('.card-footer');
      expect(footer.exists()).toBe(true);
      expect(footer.text()).toContain('Last updated');
    });

    it('does not display footer when lastUpdated not provided', () => {
      expect(wrapper.find('.card-footer').exists()).toBe(false);
    });
  });

  describe('Edge Cases', () => {
    it('handles zero target amount', async () => {
      await wrapper.setProps({
        targetAmount: 0,
        repaidAmount: 0
      });
      
      const percentage = wrapper.find('.progress-percentage');
      expect(percentage.text()).toBe('0.0%');
    });

    it('handles repaid amount exceeding target', async () => {
      await wrapper.setProps({
        repaidAmount: 200000,
        remainingAmount: -50000
      });
      
      const percentage = wrapper.find('.progress-percentage');
      expect(percentage.text()).toBe('100.0%'); // Should cap at 100%
      
      const progressBar = wrapper.find('.progress-bar');
      expect(progressBar.element.style.width).toBe('100%');
    });

    it('handles negative amounts gracefully', async () => {
      await wrapper.setProps({
        repaidAmount: -1000
      });
      
      // Should handle gracefully without breaking
      expect(wrapper.exists()).toBe(true);
    });
  });

  describe('Accessibility', () => {
    it('has proper progress bar ARIA attributes', () => {
      const progressBar = wrapper.find('.progress-bar');
      expect(progressBar.attributes('role')).toBe('progressbar');
      expect(progressBar.attributes('aria-valuenow')).toBe('33.333333333333336');
      expect(progressBar.attributes('aria-valuemin')).toBe('0');
      expect(progressBar.attributes('aria-valuemax')).toBe('100');
    });

    it('has visually hidden text for screen readers', () => {
      const hiddenText = wrapper.find('.visually-hidden');
      expect(hiddenText.exists()).toBe(true);
      expect(hiddenText.text()).toContain('33.3% complete');
    });
  });

  describe('Currency Formatting', () => {
    it('formats amounts with no decimals for whole numbers', () => {
      const amounts = wrapper.findAll('.detail-value');
      amounts.forEach(amount => {
        expect(amount.text()).not.toContain('.00');
      });
    });

    it('handles different locales', async () => {
      // This would require more sophisticated locale testing
      // For now, we verify the basic formatting works
      const repaidDetail = wrapper.findAll('.detail-value')[0];
      expect(repaidDetail.text()).toMatch(/^\$[\d,]+$/);
    });
  });
});
