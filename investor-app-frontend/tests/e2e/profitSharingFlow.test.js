import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { chromium } from 'playwright';

describe('Profit Sharing End-to-End Flow', () => {
  let browser;
  let context;
  let page;

  const baseUrl = process.env.E2E_BASE_URL || 'http://localhost:3000';
  const adminCredentials = {
    email: '<EMAIL>',
    password: 'admin123'
  };
  const investorCredentials = {
    email: '<EMAIL>',
    password: 'investor123'
  };

  beforeAll(async () => {
    browser = await chromium.launch({
      headless: process.env.CI === 'true'
    });
    context = await browser.newContext();
    page = await context.newPage();
  });

  afterAll(async () => {
    await browser.close();
  });

  describe('Admin Profit Submission Flow', () => {
    it('should allow admin to submit monthly profit', async () => {
      // Login as admin
      await page.goto(`${baseUrl}/login`);
      await page.fill('[data-testid="email-input"]', adminCredentials.email);
      await page.fill('[data-testid="password-input"]', adminCredentials.password);
      await page.click('[data-testid="login-button"]');
      
      // Wait for dashboard to load
      await page.waitForSelector('[data-testid="admin-dashboard"]');
      
      // Navigate to profit sharing admin dashboard
      await page.goto(`${baseUrl}/admin/profit-sharing`);
      await page.waitForSelector('[data-testid="profit-sharing-admin"]');
      
      // Click submit profit button
      await page.click('[data-testid="submit-profit-button"]');
      
      // Fill profit submission form
      await page.waitForSelector('[data-testid="profit-submission-modal"]');
      await page.fill('[data-testid="submission-period"]', '2024-01');
      await page.fill('[data-testid="gross-profit"]', '300000');
      await page.fill('[data-testid="net-profit"]', '150000');
      await page.fill('[data-testid="description"]', 'January 2024 profit submission');
      
      // Verify Plains North share calculation
      const plainsNorthShare = await page.textContent('[data-testid="plains-north-share"]');
      expect(plainsNorthShare).toContain('$50,000');
      
      // Submit the form
      await page.click('[data-testid="submit-profit-form"]');
      
      // Wait for success message
      await page.waitForSelector('[data-testid="success-message"]');
      
      // Verify submission appears in the table
      await page.waitForSelector('[data-testid="profit-submissions-table"]');
      const submissionRow = page.locator('[data-testid="submission-row"]').first();
      await expect(submissionRow).toContainText('2024-01');
      await expect(submissionRow).toContainText('$150,000');
      await expect(submissionRow).toContainText('Pending');
    });

    it('should allow admin to approve profit submission', async () => {
      // Find the pending submission
      const submissionRow = page.locator('[data-testid="submission-row"]').first();
      
      // Click approve button
      await submissionRow.locator('[data-testid="approve-button"]').click();
      
      // Wait for approval confirmation
      await page.waitForSelector('[data-testid="approval-success"]');
      
      // Verify status changed to approved
      await expect(submissionRow).toContainText('Distributed');
      
      // Verify distribution was created
      const distributionTable = page.locator('[data-testid="distributions-table"]');
      const distributionRow = distributionTable.locator('[data-testid="distribution-row"]').first();
      await expect(distributionRow).toContainText('$50,000');
      await expect(distributionRow).toContainText('Monthly Profit');
    });

    it('should update capital recovery progress', async () => {
      // Check capital recovery progress
      const progressBar = page.locator('[data-testid="capital-progress-bar"]');
      const progressText = await progressBar.getAttribute('aria-valuenow');
      
      // Should show some progress (33.33% for $50k out of $150k)
      expect(parseFloat(progressText)).toBeGreaterThan(30);
      expect(parseFloat(progressText)).toBeLessThan(40);
      
      // Verify amounts displayed
      const repaidAmount = await page.textContent('[data-testid="repaid-amount"]');
      expect(repaidAmount).toContain('$50,000');
      
      const remainingAmount = await page.textContent('[data-testid="remaining-amount"]');
      expect(remainingAmount).toContain('$100,000');
    });
  });

  describe('Investor Dashboard Flow', () => {
    it('should display investor dashboard with updated data', async () => {
      // Logout admin and login as investor
      await page.click('[data-testid="logout-button"]');
      await page.goto(`${baseUrl}/login`);
      
      await page.fill('[data-testid="email-input"]', investorCredentials.email);
      await page.fill('[data-testid="password-input"]', investorCredentials.password);
      await page.click('[data-testid="login-button"]');
      
      // Navigate to investor profit sharing dashboard
      await page.goto(`${baseUrl}/profit-sharing/dashboard`);
      await page.waitForSelector('[data-testid="investor-dashboard"]');
      
      // Verify KYC status
      const kycPanel = page.locator('[data-testid="kyc-panel"]');
      await expect(kycPanel).toContainText('Verified');
      
      // Verify capital recovery progress is visible
      const capitalProgress = page.locator('[data-testid="capital-progress"]');
      await expect(capitalProgress).toBeVisible();
      
      // Verify token holdings
      const tokenHoldings = page.locator('[data-testid="token-holdings"]');
      await expect(tokenHoldings).toBeVisible();
      
      // Check if token balance is displayed
      const tokenBalance = await page.textContent('[data-testid="token-balance"]');
      expect(tokenBalance).toBeTruthy();
    });

    it('should display payout history when available', async () => {
      // Check payout history table
      const payoutTable = page.locator('[data-testid="payout-history-table"]');
      
      // If payouts exist, verify they're displayed correctly
      const payoutRows = payoutTable.locator('[data-testid="payout-row"]');
      const rowCount = await payoutRows.count();
      
      if (rowCount > 0) {
        const firstPayout = payoutRows.first();
        
        // Verify payout has required fields
        await expect(firstPayout).toContainText('$'); // Amount
        await expect(firstPayout).toContainText('Monthly Profit'); // Type
        
        // Verify transaction hash link if present
        const txLink = firstPayout.locator('[data-testid="tx-hash-link"]');
        if (await txLink.count() > 0) {
          await expect(txLink).toBeVisible();
        }
      }
    });
  });

  describe('Capital Recovery Completion Flow', () => {
    it('should handle stage transition to post-recovery', async () => {
      // Login as admin again
      await page.click('[data-testid="logout-button"]');
      await page.goto(`${baseUrl}/login`);
      
      await page.fill('[data-testid="email-input"]', adminCredentials.email);
      await page.fill('[data-testid="password-input"]', adminCredentials.password);
      await page.click('[data-testid="login-button"]');
      
      await page.goto(`${baseUrl}/admin/profit-sharing`);
      
      // Submit enough profit to complete capital recovery
      await page.click('[data-testid="submit-profit-button"]');
      
      await page.fill('[data-testid="submission-period"]', '2024-02');
      await page.fill('[data-testid="gross-profit"]', '600000');
      await page.fill('[data-testid="net-profit"]', '300000');
      
      await page.click('[data-testid="submit-profit-form"]');
      await page.waitForSelector('[data-testid="success-message"]');
      
      // Approve the submission
      const newSubmissionRow = page.locator('[data-testid="submission-row"]').first();
      await newSubmissionRow.locator('[data-testid="approve-button"]').click();
      
      // Wait for stage transition
      await page.waitForTimeout(2000);
      
      // Verify stage changed to post-recovery
      const stageBadge = page.locator('[data-testid="stage-badge"]');
      await expect(stageBadge).toContainText('Post-Recovery');
      
      // Verify progress bar shows 100%
      const progressBar = page.locator('[data-testid="capital-progress-bar"]');
      const progressValue = await progressBar.getAttribute('aria-valuenow');
      expect(parseFloat(progressValue)).toBe(100);
      
      // Verify distribution breakdown shows 50/50 split
      const distributionInfo = page.locator('[data-testid="distribution-breakdown"]');
      await expect(distributionInfo).toContainText('50% to Retained Share');
      await expect(distributionInfo).toContainText('50% to Tokenised Pool');
    });
  });

  describe('System Health Monitoring', () => {
    it('should display system health status', async () => {
      // Check system health section
      const healthSection = page.locator('[data-testid="system-health"]');
      await expect(healthSection).toBeVisible();
      
      // Verify blockchain service status
      const blockchainStatus = page.locator('[data-testid="blockchain-status"]');
      await expect(blockchainStatus).toBeVisible();
      
      // Verify event listener status
      const eventListenerStatus = page.locator('[data-testid="event-listener-status"]');
      await expect(eventListenerStatus).toBeVisible();
      
      // Test sync tokens button
      const syncButton = page.locator('[data-testid="sync-tokens-button"]');
      await syncButton.click();
      
      // Wait for sync completion
      await page.waitForSelector('[data-testid="sync-success"]', { timeout: 10000 });
    });
  });

  describe('Data Export Functionality', () => {
    it('should export profit submissions data', async () => {
      // Set up download handling
      const downloadPromise = page.waitForEvent('download');
      
      // Click export button on profit submissions table
      const exportButton = page.locator('[data-testid="export-profit-submissions"]');
      await exportButton.click();
      
      // Wait for download
      const download = await downloadPromise;
      
      // Verify download
      expect(download.suggestedFilename()).toContain('profit-submissions');
      expect(download.suggestedFilename()).toContain('.csv');
    });

    it('should export distributions data', async () => {
      const downloadPromise = page.waitForEvent('download');
      
      const exportButton = page.locator('[data-testid="export-distributions"]');
      await exportButton.click();
      
      const download = await downloadPromise;
      
      expect(download.suggestedFilename()).toContain('distributions');
      expect(download.suggestedFilename()).toContain('.csv');
    });
  });

  describe('Error Handling', () => {
    it('should handle network errors gracefully', async () => {
      // Simulate network failure
      await page.route('**/profit-sharing/**', route => {
        route.abort('failed');
      });
      
      // Try to refresh data
      await page.click('[data-testid="refresh-button"]');
      
      // Verify error message is displayed
      await page.waitForSelector('[data-testid="error-message"]');
      const errorMessage = await page.textContent('[data-testid="error-message"]');
      expect(errorMessage).toContain('error');
      
      // Remove network simulation
      await page.unroute('**/profit-sharing/**');
    });

    it('should validate form inputs', async () => {
      // Try to submit invalid profit data
      await page.click('[data-testid="submit-profit-button"]');
      
      // Submit with invalid data
      await page.fill('[data-testid="net-profit"]', '-1000');
      await page.click('[data-testid="submit-profit-form"]');
      
      // Verify validation error
      const validationError = page.locator('[data-testid="validation-error"]');
      await expect(validationError).toContainText('must be greater than 0');
    });
  });

  describe('Responsive Design', () => {
    it('should work on mobile devices', async () => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      
      // Navigate to dashboard
      await page.goto(`${baseUrl}/admin/profit-sharing`);
      
      // Verify mobile layout
      const dashboard = page.locator('[data-testid="profit-sharing-admin"]');
      await expect(dashboard).toBeVisible();
      
      // Verify responsive tables
      const table = page.locator('[data-testid="profit-submissions-table"]');
      await expect(table).toBeVisible();
      
      // Reset viewport
      await page.setViewportSize({ width: 1280, height: 720 });
    });
  });
});
