<template>
  <div class="admin-dashboard">
    <div class="container-fluid">
      <!-- Header Section -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h1 class="h3 mb-1">Profit Sharing Administration</h1>
              <p class="text-muted mb-0">Manage profit distributions and monitor system status</p>
            </div>
            <div class="dashboard-actions">
              <button 
                class="btn btn-success me-2"
                @click="showProfitSubmissionModal = true"
                :disabled="loading"
              >
                <i class="bi bi-plus-circle me-1"></i>
                Submit Profit
              </button>
              <button 
                class="btn btn-outline-primary"
                @click="refreshData"
                :disabled="loading"
              >
                <i class="bi bi-arrow-clockwise me-1"></i>
                Refresh
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- System Status Cards -->
      <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
          <KpiCard
            title="Total Distributed"
            :value="statistics.total_plains_north_share || 0"
            format="currency"
            currency="CAD"
            icon="bi bi-cash-stack"
            icon-color="#198754"
            variant="success"
            :loading="loading"
          />
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
          <KpiCard
            title="Active Investors"
            :value="statistics.unique_investors_paid || 0"
            format="number"
            icon="bi bi-people"
            icon-color="#0d6efd"
            variant="primary"
            :loading="loading"
          />
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
          <KpiCard
            title="Verified KYC"
            :value="statistics.verified_investors || 0"
            format="number"
            icon="bi bi-shield-check"
            icon-color="#20c997"
            variant="default"
            :loading="loading"
          />
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
          <KpiCard
            title="Recovery Progress"
            :value="capitalStatus.recovery_percentage || 0"
            format="percentage"
            icon="bi bi-piggy-bank"
            icon-color="#6f42c1"
            variant="default"
            :loading="loading"
          />
        </div>
      </div>

      <!-- Capital Recovery Progress -->
      <div class="row mb-4">
        <div class="col-12">
          <CapitalProgress
            :target-amount="capitalStatus.target_amount"
            :repaid-amount="capitalStatus.repaid_amount"
            :remaining-amount="capitalStatus.remaining_amount"
            :stage="capitalStatus.stage"
            :show-timeline="true"
            :milestones="capitalMilestones"
            :last-updated="capitalStatus.last_updated"
            :loading="loading"
          />
        </div>
      </div>

      <!-- Data Tables -->
      <div class="row">
        <div class="col-lg-6 mb-4">
          <DataTable
            title="Recent Profit Submissions"
            title-icon="bi bi-file-earmark-text"
            :data="profitSubmissions"
            :columns="profitSubmissionColumns"
            :loading="loading"
            :show-search="true"
            :show-pagination="true"
            :show-actions="true"
            :show-export="true"
            :page-size="10"
            empty-message="No profit submissions found"
            @view="viewProfitSubmission"
            @edit="editProfitSubmission"
            @export="exportProfitSubmissions"
          >
            <template #cell-plains_north_share="{ value }">
              <span class="fw-bold text-primary">{{ formatCurrency(value) }}</span>
            </template>
            
            <template #cell-status="{ value }">
              <span class="badge" :class="getStatusBadgeClass(value)">
                {{ formatStatus(value) }}
              </span>
            </template>
            
            <template #cell-submission_date="{ value }">
              {{ formatDate(value) }}
            </template>
            
            <template #actions="{ item }">
              <div class="btn-group btn-group-sm" role="group">
                <button 
                  class="btn btn-outline-primary"
                  @click="viewProfitSubmission(item)"
                  title="View Details"
                >
                  <i class="bi bi-eye"></i>
                </button>
                <button 
                  v-if="item.status === 'pending'"
                  class="btn btn-outline-success"
                  @click="approveProfitSubmission(item)"
                  title="Approve"
                >
                  <i class="bi bi-check-lg"></i>
                </button>
                <button 
                  v-if="item.status === 'pending'"
                  class="btn btn-outline-danger"
                  @click="rejectProfitSubmission(item)"
                  title="Reject"
                >
                  <i class="bi bi-x-lg"></i>
                </button>
              </div>
            </template>
          </DataTable>
        </div>

        <div class="col-lg-6 mb-4">
          <DataTable
            title="Recent Distributions"
            title-icon="bi bi-arrow-down-circle"
            :data="distributions"
            :columns="distributionColumns"
            :loading="loading"
            :show-search="true"
            :show-pagination="true"
            :show-actions="true"
            :show-export="true"
            :page-size="10"
            empty-message="No distributions found"
            @view="viewDistribution"
            @export="exportDistributions"
          >
            <template #cell-total_amount="{ value }">
              <span class="fw-bold text-success">{{ formatCurrency(value) }}</span>
            </template>
            
            <template #cell-status="{ value }">
              <span class="badge" :class="getStatusBadgeClass(value)">
                {{ formatStatus(value) }}
              </span>
            </template>
            
            <template #cell-blockchain_tx_hash="{ value }">
              <TxHashLink
                :tx-hash="value"
                :truncate="true"
                :truncate-length="6"
                :show-status="false"
                network="ethereum"
              />
            </template>
            
            <template #cell-distribution_date="{ value }">
              {{ formatDate(value) }}
            </template>
            
            <template #actions="{ item }">
              <div class="btn-group btn-group-sm" role="group">
                <button 
                  class="btn btn-outline-primary"
                  @click="viewDistribution(item)"
                  title="View Details"
                >
                  <i class="bi bi-eye"></i>
                </button>
                <button 
                  class="btn btn-outline-info"
                  @click="viewDistributionPayouts(item)"
                  title="View Payouts"
                >
                  <i class="bi bi-list-ul"></i>
                </button>
              </div>
            </template>
          </DataTable>
        </div>
      </div>

      <!-- System Health Section -->
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="card-title mb-0">
                <i class="bi bi-activity me-2"></i>
                System Health
              </h5>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-6">
                  <h6>Blockchain Service</h6>
                  <div class="d-flex align-items-center mb-3">
                    <span class="badge" :class="blockchainHealth.initialized ? 'bg-success' : 'bg-danger'">
                      {{ blockchainHealth.initialized ? 'Connected' : 'Disconnected' }}
                    </span>
                    <small class="text-muted ms-2">
                      {{ blockchainHealth.contracts_loaded || 0 }} contracts loaded
                    </small>
                  </div>
                </div>
                <div class="col-md-6">
                  <h6>Event Listener</h6>
                  <div class="d-flex align-items-center mb-3">
                    <span class="badge" :class="eventListenerHealth.listening ? 'bg-success' : 'bg-warning'">
                      {{ eventListenerHealth.listening ? 'Active' : 'Inactive' }}
                    </span>
                    <small class="text-muted ms-2">
                      Block {{ eventListenerHealth.last_processed_block || 0 }}
                    </small>
                  </div>
                </div>
              </div>
              
              <div class="d-flex gap-2">
                <button 
                  class="btn btn-outline-primary btn-sm"
                  @click="syncTokenHoldings"
                  :disabled="loading"
                >
                  <i class="bi bi-arrow-repeat me-1"></i>
                  Sync Tokens
                </button>
                <button 
                  class="btn btn-outline-warning btn-sm"
                  @click="restartEventListener"
                  :disabled="loading"
                >
                  <i class="bi bi-bootstrap-reboot me-1"></i>
                  Restart Listener
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Profit Submission Modal -->
    <ProfitSubmissionModal
      v-model:show="showProfitSubmissionModal"
      @submit="handleProfitSubmission"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import { useProfitSharingStore } from '@/stores/profitSharing';
import KpiCard from '@/components/profit-sharing/KpiCard.vue';
import CapitalProgress from '@/components/profit-sharing/CapitalProgress.vue';
import DataTable from '@/components/profit-sharing/DataTable.vue';
import TxHashLink from '@/components/profit-sharing/TxHashLink.vue';
import ProfitSubmissionModal from '@/components/profit-sharing/ProfitSubmissionModal.vue';

const router = useRouter();
const authStore = useAuthStore();
const profitSharingStore = useProfitSharingStore();

// Reactive data
const loading = ref(false);
const error = ref(null);
const showProfitSubmissionModal = ref(false);

// Computed properties
const capitalStatus = computed(() => profitSharingStore.capitalStatus || {});
const statistics = computed(() => profitSharingStore.statistics || {});
const profitSubmissions = computed(() => profitSharingStore.profitSubmissions || []);
const distributions = computed(() => profitSharingStore.distributions || []);
const blockchainHealth = computed(() => profitSharingStore.blockchainHealth || {});
const eventListenerHealth = computed(() => profitSharingStore.eventListenerHealth || {});

const capitalMilestones = computed(() => {
  const target = capitalStatus.value.target_amount || 150000;
  const repaid = capitalStatus.value.repaid_amount || 0;
  
  return [
    {
      title: '25% Recovery',
      amount: target * 0.25,
      completed: repaid >= target * 0.25,
      date: repaid >= target * 0.25 ? new Date() : null
    },
    {
      title: '50% Recovery',
      amount: target * 0.5,
      completed: repaid >= target * 0.5,
      date: repaid >= target * 0.5 ? new Date() : null
    },
    {
      title: '75% Recovery',
      amount: target * 0.75,
      completed: repaid >= target * 0.75,
      date: repaid >= target * 0.75 ? new Date() : null
    },
    {
      title: 'Full Recovery',
      amount: target,
      completed: repaid >= target,
      date: repaid >= target ? new Date() : null
    }
  ];
});

// Table columns
const profitSubmissionColumns = [
  { key: 'submission_period', label: 'Period', sortable: true },
  { key: 'net_profit', label: 'Net Profit', sortable: true, format: 'currency' },
  { key: 'plains_north_share', label: 'PN Share', sortable: true },
  { key: 'status', label: 'Status', sortable: true },
  { key: 'submission_date', label: 'Submitted', sortable: true }
];

const distributionColumns = [
  { key: 'distribution_type', label: 'Type', sortable: true },
  { key: 'total_amount', label: 'Amount', sortable: true },
  { key: 'stage', label: 'Stage', sortable: true },
  { key: 'status', label: 'Status', sortable: true },
  { key: 'blockchain_tx_hash', label: 'Transaction', sortable: false },
  { key: 'distribution_date', label: 'Date', sortable: true }
];

// Methods
const refreshData = async () => {
  loading.value = true;
  error.value = null;
  
  try {
    await Promise.all([
      profitSharingStore.fetchAdminDashboard(),
      profitSharingStore.fetchCapitalStatus(),
      profitSharingStore.fetchStatistics(),
      profitSharingStore.fetchSystemHealth()
    ]);
  } catch (err) {
    error.value = err.message;
    console.error('Error refreshing admin dashboard:', err);
  } finally {
    loading.value = false;
  }
};

const handleProfitSubmission = async (submissionData) => {
  try {
    await profitSharingStore.submitProfit(submissionData);
    showProfitSubmissionModal.value = false;
    await refreshData();
  } catch (err) {
    console.error('Error submitting profit:', err);
  }
};

const viewProfitSubmission = (submission) => {
  router.push(`/admin/profit-sharing/submissions/${submission.id}`);
};

const editProfitSubmission = (submission) => {
  router.push(`/admin/profit-sharing/submissions/${submission.id}/edit`);
};

const approveProfitSubmission = async (submission) => {
  try {
    await profitSharingStore.approveProfitSubmission(submission.id);
    await refreshData();
  } catch (err) {
    console.error('Error approving submission:', err);
  }
};

const rejectProfitSubmission = async (submission) => {
  try {
    await profitSharingStore.rejectProfitSubmission(submission.id);
    await refreshData();
  } catch (err) {
    console.error('Error rejecting submission:', err);
  }
};

const viewDistribution = (distribution) => {
  router.push(`/admin/profit-sharing/distributions/${distribution.id}`);
};

const viewDistributionPayouts = (distribution) => {
  router.push(`/admin/profit-sharing/distributions/${distribution.id}/payouts`);
};

const syncTokenHoldings = async () => {
  try {
    await profitSharingStore.syncTokenHoldings();
    await refreshData();
  } catch (err) {
    console.error('Error syncing token holdings:', err);
  }
};

const restartEventListener = async () => {
  try {
    await profitSharingStore.restartEventListener();
    await refreshData();
  } catch (err) {
    console.error('Error restarting event listener:', err);
  }
};

const exportProfitSubmissions = (data) => {
  profitSharingStore.exportData(data, 'profit-submissions');
};

const exportDistributions = (data) => {
  profitSharingStore.exportData(data, 'distributions');
};

const getStatusBadgeClass = (status) => {
  const statusClasses = {
    pending: 'bg-warning text-dark',
    approved: 'bg-info text-white',
    distributed: 'bg-success text-white',
    rejected: 'bg-danger text-white',
    processing: 'bg-primary text-white',
    completed: 'bg-success text-white',
    failed: 'bg-danger text-white'
  };
  
  return statusClasses[status] || 'bg-secondary text-white';
};

const formatStatus = (status) => {
  return status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ');
};

const formatCurrency = (amount) => {
  return new Intl.NumberFormat('en-CA', {
    style: 'currency',
    currency: 'CAD',
    minimumFractionDigits: 0
  }).format(amount);
};

const formatDate = (date) => {
  if (!date) return '';
  
  const d = new Date(date);
  return d.toLocaleDateString('en-CA', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

// Lifecycle
onMounted(() => {
  refreshData();
});
</script>

<style lang="scss" scoped>
.admin-dashboard {
  .dashboard-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .admin-dashboard {
    .dashboard-actions {
      flex-direction: column;
      align-items: stretch;
      
      .btn {
        margin-bottom: 0.5rem;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
