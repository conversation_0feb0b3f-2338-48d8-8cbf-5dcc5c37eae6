<template>
  <div class="investor-dashboard">
    <div class="container-fluid">
      <!-- Header Section -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h1 class="h3 mb-1">Profit Sharing Dashboard</h1>
              <p class="text-muted mb-0">Track your token holdings and profit distributions</p>
            </div>
            <div class="dashboard-actions">
              <button 
                class="btn btn-outline-primary me-2"
                @click="refreshData"
                :disabled="loading"
              >
                <i class="bi bi-arrow-clockwise me-1"></i>
                Refresh
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- KYC Status Section -->
      <div class="row mb-4">
        <div class="col-12">
          <KycPanel
            :verified="user.kyc_status === 'verified'"
            :status="user.kyc_status"
            :verification-date="user.kyc_verified_at"
            :rejection-reason="user.kyc_rejection_reason"
            :investor-id="user.id"
            :token-balance="totalTokenBalance"
            :compliance-checks="complianceChecks"
            :show-eligibility="true"
            :show-actions="true"
            :can-approve="false"
            :can-reject="false"
            :last-updated="user.updated_at"
            :loading="loading"
            @initiate-kyc="initiateKyc"
            @view-documents="viewKycDocuments"
          />
        </div>
      </div>

      <!-- Capital Recovery Progress -->
      <div class="row mb-4">
        <div class="col-12">
          <CapitalProgress
            :target-amount="capitalStatus.target_amount"
            :repaid-amount="capitalStatus.repaid_amount"
            :remaining-amount="capitalStatus.remaining_amount"
            :stage="capitalStatus.stage"
            :show-timeline="false"
            :last-updated="capitalStatus.last_updated"
            :loading="loading"
          />
        </div>
      </div>

      <!-- KPI Cards -->
      <div class="row mb-4">
        <div class="col-md-3 col-sm-6 mb-3">
          <KpiCard
            title="Token Balance"
            :value="totalTokenBalance"
            format="number"
            icon="bi bi-coin"
            icon-color="#0d6efd"
            variant="primary"
            :loading="loading"
          />
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
          <KpiCard
            title="Total Received"
            :value="totalReceived"
            format="currency"
            currency="CAD"
            icon="bi bi-cash-stack"
            icon-color="#198754"
            variant="success"
            :loading="loading"
          />
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
          <KpiCard
            title="Last Payout"
            :value="lastPayoutAmount"
            format="currency"
            currency="CAD"
            icon="bi bi-arrow-down-circle"
            icon-color="#fd7e14"
            variant="warning"
            :description="lastPayoutDate ? `on ${formatDate(lastPayoutDate)}` : 'No payouts yet'"
            :loading="loading"
          />
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
          <KpiCard
            title="Recovery Progress"
            :value="capitalStatus.recovery_percentage"
            format="percentage"
            icon="bi bi-piggy-bank"
            icon-color="#6f42c1"
            variant="default"
            :loading="loading"
          />
        </div>
      </div>

      <!-- Token Holdings and Payout History -->
      <div class="row">
        <div class="col-lg-6 mb-4">
          <div class="card">
            <div class="card-header">
              <h5 class="card-title mb-0">
                <i class="bi bi-wallet2 me-2"></i>
                Token Holdings
              </h5>
            </div>
            <div class="card-body">
              <div v-if="loading" class="text-center py-4">
                <div class="spinner-border" role="status">
                  <span class="visually-hidden">Loading...</span>
                </div>
              </div>
              
              <div v-else-if="tokenHoldings.length === 0" class="text-center py-4 text-muted">
                <i class="bi bi-wallet display-6 d-block mb-2"></i>
                <p>No token holdings found</p>
              </div>
              
              <div v-else>
                <div 
                  v-for="holding in tokenHoldings" 
                  :key="holding.id"
                  class="token-holding-item d-flex justify-content-between align-items-center py-3 border-bottom"
                >
                  <div class="token-info">
                    <h6 class="mb-1">{{ holding.token.token_name }}</h6>
                    <small class="text-muted">{{ holding.token.token_symbol }}</small>
                  </div>
                  <div class="token-balance text-end">
                    <div class="balance-amount fw-bold">
                      {{ formatTokenAmount(holding.balance) }}
                    </div>
                    <small class="text-muted">
                      Updated {{ formatDate(holding.last_updated) }}
                    </small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="col-lg-6 mb-4">
          <DataTable
            title="Recent Payouts"
            title-icon="bi bi-cash-coin"
            :data="payoutHistory"
            :columns="payoutColumns"
            :loading="loading"
            :show-search="false"
            :show-pagination="true"
            :show-actions="false"
            :show-export="false"
            :page-size="5"
            empty-message="No payouts received yet"
          >
            <template #cell-amount="{ value }">
              <span class="fw-bold text-success">{{ formatCurrency(value) }}</span>
            </template>
            
            <template #cell-blockchain_tx_hash="{ value }">
              <TxHashLink
                :tx-hash="value"
                :truncate="true"
                :truncate-length="6"
                :show-status="false"
                network="ethereum"
              />
            </template>
            
            <template #cell-created_at="{ value }">
              {{ formatDate(value) }}
            </template>
          </DataTable>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import { useProfitSharingStore } from '@/stores/profitSharing';
import KpiCard from '@/components/profit-sharing/KpiCard.vue';
import CapitalProgress from '@/components/profit-sharing/CapitalProgress.vue';
import DataTable from '@/components/profit-sharing/DataTable.vue';
import KycPanel from '@/components/profit-sharing/KycPanel.vue';
import TxHashLink from '@/components/profit-sharing/TxHashLink.vue';

const router = useRouter();
const authStore = useAuthStore();
const profitSharingStore = useProfitSharingStore();

// Reactive data
const loading = ref(false);
const error = ref(null);

// Computed properties
const user = computed(() => authStore.user || {});
const capitalStatus = computed(() => profitSharingStore.capitalStatus || {});
const tokenHoldings = computed(() => profitSharingStore.tokenHoldings || []);
const payoutHistory = computed(() => profitSharingStore.payoutHistory || []);
const complianceChecks = computed(() => profitSharingStore.complianceChecks || null);

const totalTokenBalance = computed(() => {
  return tokenHoldings.value.reduce((sum, holding) => 
    sum + parseFloat(holding.balance || 0), 0);
});

const totalReceived = computed(() => {
  return payoutHistory.value.reduce((sum, payout) => 
    sum + parseFloat(payout.amount || 0), 0);
});

const lastPayoutAmount = computed(() => {
  if (payoutHistory.value.length === 0) return 0;
  return parseFloat(payoutHistory.value[0].amount || 0);
});

const lastPayoutDate = computed(() => {
  if (payoutHistory.value.length === 0) return null;
  return payoutHistory.value[0].created_at;
});

// Table columns
const payoutColumns = [
  {
    key: 'amount',
    label: 'Amount',
    sortable: true,
    align: 'right'
  },
  {
    key: 'distribution.distribution_type',
    label: 'Type',
    sortable: true
  },
  {
    key: 'blockchain_tx_hash',
    label: 'Transaction',
    sortable: false
  },
  {
    key: 'created_at',
    label: 'Date',
    sortable: true
  }
];

// Methods
const refreshData = async () => {
  loading.value = true;
  error.value = null;
  
  try {
    await Promise.all([
      profitSharingStore.fetchCapitalStatus(),
      profitSharingStore.fetchTokenHoldings(user.value.id),
      profitSharingStore.fetchPayoutHistory(user.value.id),
      profitSharingStore.fetchInvestorDashboard(user.value.id)
    ]);
  } catch (err) {
    error.value = err.message;
    console.error('Error refreshing dashboard data:', err);
  } finally {
    loading.value = false;
  }
};

const initiateKyc = () => {
  router.push('/kyc/verification');
};

const viewKycDocuments = () => {
  router.push('/kyc/documents');
};

const formatCurrency = (amount) => {
  return new Intl.NumberFormat('en-CA', {
    style: 'currency',
    currency: 'CAD',
    minimumFractionDigits: 2
  }).format(amount);
};

const formatTokenAmount = (amount) => {
  if (amount >= 1000000) {
    return `${(amount / 1000000).toFixed(1)}M`;
  } else if (amount >= 1000) {
    return `${(amount / 1000).toFixed(1)}K`;
  }
  return amount.toLocaleString();
};

const formatDate = (date) => {
  if (!date) return '';
  
  const d = new Date(date);
  return d.toLocaleDateString('en-CA', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

// Lifecycle
onMounted(() => {
  refreshData();
});
</script>

<style lang="scss" scoped>
.investor-dashboard {
  .dashboard-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .token-holding-item {
    &:last-child {
      border-bottom: none !important;
    }
    
    .token-info h6 {
      margin-bottom: 0.25rem;
    }
    
    .balance-amount {
      font-size: 1.1rem;
      color: var(--bs-primary);
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .investor-dashboard {
    .dashboard-actions {
      flex-direction: column;
      align-items: stretch;
      
      .btn {
        margin-bottom: 0.5rem;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
    
    .token-holding-item {
      flex-direction: column;
      align-items: flex-start;
      
      .token-balance {
        text-align: left !important;
        margin-top: 0.5rem;
      }
    }
  }
}
</style>
