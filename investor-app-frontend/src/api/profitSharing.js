import { apiClient } from './client';

/**
 * Profit Sharing API Client
 * Handles all API calls related to profit sharing functionality
 */
export const profitSharingApi = {
  // Capital Recovery Endpoints
  async getCapitalStatus() {
    return apiClient.get('/profit-sharing/capital/status');
  },

  async updateCapitalRecovery(amount, txHash = null) {
    return apiClient.post('/profit-sharing/capital/update', {
      amount,
      tx_hash: txHash
    });
  },

  async getCapitalHistory(limit = 50, offset = 0) {
    return apiClient.get('/profit-sharing/capital/history', {
      params: { limit, offset }
    });
  },

  async getCapitalStatistics() {
    return apiClient.get('/profit-sharing/capital/statistics');
  },

  // Token Holdings Endpoints
  async getTokenBalance(investorId) {
    return apiClient.get(`/profit-sharing/token/balance/${investorId}`);
  },

  async syncTokenHoldings() {
    return apiClient.post('/profit-sharing/blockchain/sync-tokens');
  },

  // Payout History Endpoints
  async getPayoutHistory(investorId, limit = 50, offset = 0) {
    return apiClient.get(`/profit-sharing/payout/history/${investorId}`, {
      params: { limit, offset }
    });
  },

  // Profit Submission Endpoints
  async submitProfit(profitData) {
    return apiClient.post('/profit-sharing/accounting/profit', profitData);
  },

  async approveProfitSubmission(submissionId) {
    return apiClient.post(`/profit-sharing/accounting/profit/${submissionId}/approve`);
  },

  async rejectProfitSubmission(submissionId, reason = '') {
    return apiClient.post(`/profit-sharing/accounting/profit/${submissionId}/reject`, {
      reason
    });
  },

  async getProfitSubmissions(limit = 50, offset = 0) {
    return apiClient.get('/profit-sharing/accounting/profit/submissions', {
      params: { limit, offset }
    });
  },

  async getProfitSubmissionDetails(submissionId) {
    return apiClient.get(`/profit-sharing/accounting/profit/submissions/${submissionId}`);
  },

  // Distribution Endpoints
  async getDistributions(limit = 50, offset = 0) {
    return apiClient.get('/profit-sharing/distributions', {
      params: { limit, offset }
    });
  },

  async getDistributionDetails(distributionId) {
    return apiClient.get(`/profit-sharing/distributions/${distributionId}`);
  },

  async getDistributionPayouts(distributionId) {
    return apiClient.get(`/profit-sharing/distributions/${distributionId}/payouts`);
  },

  async triggerDistribution(submissionId) {
    return apiClient.post(`/profit-sharing/distributions/trigger`, {
      submission_id: submissionId
    });
  },

  // Dashboard Endpoints
  async getInvestorDashboard(investorId) {
    return apiClient.get(`/profit-sharing/dashboard/investor/${investorId}`);
  },

  async getAdminDashboard() {
    return apiClient.get('/profit-sharing/dashboard/admin');
  },

  // Statistics Endpoints
  async getStatistics() {
    return apiClient.get('/profit-sharing/statistics');
  },

  async getProfitSharingStatistics() {
    return apiClient.get('/profit-sharing/statistics/profit-sharing');
  },

  async getCapitalRecoveryStatistics() {
    return apiClient.get('/profit-sharing/statistics/capital-recovery');
  },

  // KYC Endpoints
  async updateKycStatus(investorId, verified) {
    return apiClient.post('/profit-sharing/kyc/verify', {
      investor_id: investorId,
      verified
    });
  },

  async checkDistributionEligibility(investorId) {
    return apiClient.get(`/profit-sharing/kyc/eligibility/${investorId}`);
  },

  async getKycStatistics() {
    return apiClient.get('/profit-sharing/kyc/statistics');
  },

  // Blockchain Endpoints
  async getSystemHealth() {
    return apiClient.get('/profit-sharing/blockchain/health');
  },

  async restartEventListener() {
    return apiClient.post('/profit-sharing/events/restart');
  },

  async getRecentEvents(limit = 50, offset = 0, eventType = null) {
    const params = { limit, offset };
    if (eventType) {
      params.event_type = eventType;
    }
    
    return apiClient.get('/profit-sharing/events/recent', { params });
  },

  async getBlockchainEvents(contractAddress = null, eventType = null, limit = 50, offset = 0) {
    const params = { limit, offset };
    if (contractAddress) params.contract_address = contractAddress;
    if (eventType) params.event_type = eventType;
    
    return apiClient.get('/profit-sharing/blockchain/events', { params });
  },

  async processEvent(eventId) {
    return apiClient.post(`/profit-sharing/blockchain/events/${eventId}/process`);
  },

  async retryFailedEvent(eventId) {
    return apiClient.post(`/profit-sharing/blockchain/events/${eventId}/retry`);
  },

  // Token Management Endpoints
  async getTokenDetails(tokenId) {
    return apiClient.get(`/profit-sharing/tokens/${tokenId}`);
  },

  async updateTokenMetadata(tokenId, metadata) {
    return apiClient.put(`/profit-sharing/tokens/${tokenId}`, metadata);
  },

  async getTokenHolders(tokenId, limit = 50, offset = 0) {
    return apiClient.get(`/profit-sharing/tokens/${tokenId}/holders`, {
      params: { limit, offset }
    });
  },

  async updateTokenHolding(holdingId, balance) {
    return apiClient.put(`/profit-sharing/token-holdings/${holdingId}`, {
      balance
    });
  },

  // Role Management Endpoints
  async getUserRoles(userId) {
    return apiClient.get(`/profit-sharing/roles/user/${userId}`);
  },

  async assignRole(userId, roleType, permissions = {}) {
    return apiClient.post('/profit-sharing/roles/assign', {
      user_id: userId,
      role_type: roleType,
      permissions
    });
  },

  async revokeRole(userId, roleType) {
    return apiClient.post('/profit-sharing/roles/revoke', {
      user_id: userId,
      role_type: roleType
    });
  },

  async getRolePermissions(roleType) {
    return apiClient.get(`/profit-sharing/roles/${roleType}/permissions`);
  },

  // Reporting Endpoints
  async generateProfitReport(startDate, endDate, format = 'json') {
    return apiClient.get('/profit-sharing/reports/profit', {
      params: {
        start_date: startDate,
        end_date: endDate,
        format
      }
    });
  },

  async generateDistributionReport(distributionId, format = 'json') {
    return apiClient.get(`/profit-sharing/reports/distribution/${distributionId}`, {
      params: { format }
    });
  },

  async generateInvestorReport(investorId, startDate, endDate, format = 'json') {
    return apiClient.get(`/profit-sharing/reports/investor/${investorId}`, {
      params: {
        start_date: startDate,
        end_date: endDate,
        format
      }
    });
  },

  async generateComplianceReport(startDate, endDate, format = 'json') {
    return apiClient.get('/profit-sharing/reports/compliance', {
      params: {
        start_date: startDate,
        end_date: endDate,
        format
      }
    });
  },

  // Audit Trail Endpoints
  async getAuditTrail(entityType, entityId, limit = 50, offset = 0) {
    return apiClient.get('/profit-sharing/audit/trail', {
      params: {
        entity_type: entityType,
        entity_id: entityId,
        limit,
        offset
      }
    });
  },

  async getSystemAuditLog(startDate, endDate, limit = 100, offset = 0) {
    return apiClient.get('/profit-sharing/audit/system', {
      params: {
        start_date: startDate,
        end_date: endDate,
        limit,
        offset
      }
    });
  },

  // Configuration Endpoints
  async getSystemConfiguration() {
    return apiClient.get('/profit-sharing/config/system');
  },

  async updateSystemConfiguration(config) {
    return apiClient.put('/profit-sharing/config/system', config);
  },

  async getContractAddresses() {
    return apiClient.get('/profit-sharing/config/contracts');
  },

  async updateContractAddresses(addresses) {
    return apiClient.put('/profit-sharing/config/contracts', addresses);
  },

  // Notification Endpoints
  async getNotifications(userId, limit = 20, offset = 0) {
    return apiClient.get(`/profit-sharing/notifications/${userId}`, {
      params: { limit, offset }
    });
  },

  async markNotificationRead(notificationId) {
    return apiClient.put(`/profit-sharing/notifications/${notificationId}/read`);
  },

  async createNotification(userId, type, title, message, metadata = {}) {
    return apiClient.post('/profit-sharing/notifications', {
      user_id: userId,
      type,
      title,
      message,
      metadata
    });
  },

  // Utility Endpoints
  async validateSubmissionPeriod(period) {
    return apiClient.get('/profit-sharing/utils/validate-period', {
      params: { period }
    });
  },

  async calculatePlainsNorthShare(netProfit) {
    return apiClient.get('/profit-sharing/utils/calculate-share', {
      params: { net_profit: netProfit }
    });
  },

  async estimateGasCost(operation, params = {}) {
    return apiClient.get('/profit-sharing/utils/estimate-gas', {
      params: { operation, ...params }
    });
  },

  async getExchangeRates(baseCurrency = 'CAD') {
    return apiClient.get('/profit-sharing/utils/exchange-rates', {
      params: { base: baseCurrency }
    });
  }
};

export default profitSharingApi;
