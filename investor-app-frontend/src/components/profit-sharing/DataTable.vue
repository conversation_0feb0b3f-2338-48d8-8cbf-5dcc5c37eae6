<template>
  <div class="data-table">
    <div class="card">
      <div class="card-header d-flex justify-content-between align-items-center" v-if="title || showSearch || showExport">
        <h5 class="card-title mb-0" v-if="title">
          <i :class="titleIcon" class="me-2" v-if="titleIcon"></i>
          {{ title }}
        </h5>
        
        <div class="table-controls d-flex gap-2">
          <div class="search-box" v-if="showSearch">
            <div class="input-group input-group-sm">
              <span class="input-group-text">
                <i class="bi bi-search"></i>
              </span>
              <input 
                type="text" 
                class="form-control" 
                placeholder="Search..."
                v-model="searchQuery"
              >
            </div>
          </div>
          
          <button 
            class="btn btn-outline-secondary btn-sm" 
            v-if="showExport"
            @click="exportData"
            :disabled="loading"
          >
            <i class="bi bi-download me-1"></i>
            Export
          </button>
        </div>
      </div>
      
      <div class="card-body p-0">
        <div class="table-responsive">
          <table class="table table-hover mb-0">
            <thead class="table-light">
              <tr>
                <th 
                  v-for="column in columns" 
                  :key="column.key"
                  :class="getHeaderClass(column)"
                  @click="column.sortable ? sort(column.key) : null"
                  style="cursor: pointer;"
                >
                  {{ column.label }}
                  <i 
                    v-if="column.sortable" 
                    :class="getSortIcon(column.key)"
                    class="ms-1"
                  ></i>
                </th>
                <th v-if="showActions" class="text-center">Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr v-if="loading">
                <td :colspan="columns.length + (showActions ? 1 : 0)" class="text-center py-4">
                  <div class="spinner-border spinner-border-sm me-2" role="status">
                    <span class="visually-hidden">Loading...</span>
                  </div>
                  Loading...
                </td>
              </tr>
              
              <tr v-else-if="filteredData.length === 0">
                <td :colspan="columns.length + (showActions ? 1 : 0)" class="text-center py-4 text-muted">
                  <i class="bi bi-inbox display-6 d-block mb-2"></i>
                  {{ emptyMessage }}
                </td>
              </tr>
              
              <tr 
                v-else
                v-for="(item, index) in paginatedData" 
                :key="getRowKey(item, index)"
                :class="getRowClass(item, index)"
              >
                <td 
                  v-for="column in columns" 
                  :key="column.key"
                  :class="getCellClass(column, item)"
                >
                  <slot 
                    :name="`cell-${column.key}`" 
                    :item="item" 
                    :value="getNestedValue(item, column.key)"
                    :column="column"
                    :index="index"
                  >
                    {{ formatCellValue(item, column) }}
                  </slot>
                </td>
                
                <td v-if="showActions" class="text-center">
                  <slot name="actions" :item="item" :index="index">
                    <div class="btn-group btn-group-sm" role="group">
                      <button 
                        class="btn btn-outline-primary"
                        @click="$emit('view', item)"
                        title="View"
                      >
                        <i class="bi bi-eye"></i>
                      </button>
                      <button 
                        class="btn btn-outline-secondary"
                        @click="$emit('edit', item)"
                        title="Edit"
                      >
                        <i class="bi bi-pencil"></i>
                      </button>
                    </div>
                  </slot>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      
      <div class="card-footer d-flex justify-content-between align-items-center" v-if="showPagination && totalPages > 1">
        <div class="pagination-info">
          <small class="text-muted">
            Showing {{ startIndex + 1 }} to {{ endIndex }} of {{ filteredData.length }} entries
          </small>
        </div>
        
        <nav aria-label="Table pagination">
          <ul class="pagination pagination-sm mb-0">
            <li class="page-item" :class="{ disabled: currentPage === 1 }">
              <button class="page-link" @click="goToPage(currentPage - 1)" :disabled="currentPage === 1">
                <i class="bi bi-chevron-left"></i>
              </button>
            </li>
            
            <li 
              v-for="page in visiblePages" 
              :key="page"
              class="page-item" 
              :class="{ active: page === currentPage }"
            >
              <button class="page-link" @click="goToPage(page)">
                {{ page }}
              </button>
            </li>
            
            <li class="page-item" :class="{ disabled: currentPage === totalPages }">
              <button class="page-link" @click="goToPage(currentPage + 1)" :disabled="currentPage === totalPages">
                <i class="bi bi-chevron-right"></i>
              </button>
            </li>
          </ul>
        </nav>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, watch } from 'vue';

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  titleIcon: {
    type: String,
    default: ''
  },
  data: {
    type: Array,
    default: () => []
  },
  columns: {
    type: Array,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  },
  showSearch: {
    type: Boolean,
    default: true
  },
  showPagination: {
    type: Boolean,
    default: true
  },
  showActions: {
    type: Boolean,
    default: false
  },
  showExport: {
    type: Boolean,
    default: false
  },
  pageSize: {
    type: Number,
    default: 10
  },
  emptyMessage: {
    type: String,
    default: 'No data available'
  },
  rowKey: {
    type: String,
    default: 'id'
  }
});

const emit = defineEmits(['view', 'edit', 'export', 'sort']);

// Reactive data
const searchQuery = ref('');
const sortKey = ref('');
const sortOrder = ref('asc');
const currentPage = ref(1);

// Computed properties
const filteredData = computed(() => {
  if (!searchQuery.value) return props.data;
  
  const query = searchQuery.value.toLowerCase();
  return props.data.filter(item => {
    return props.columns.some(column => {
      const value = getNestedValue(item, column.key);
      return String(value).toLowerCase().includes(query);
    });
  });
});

const sortedData = computed(() => {
  if (!sortKey.value) return filteredData.value;
  
  return [...filteredData.value].sort((a, b) => {
    const aVal = getNestedValue(a, sortKey.value);
    const bVal = getNestedValue(b, sortKey.value);
    
    let result = 0;
    if (aVal < bVal) result = -1;
    if (aVal > bVal) result = 1;
    
    return sortOrder.value === 'desc' ? -result : result;
  });
});

const totalPages = computed(() => {
  return Math.ceil(filteredData.value.length / props.pageSize);
});

const startIndex = computed(() => {
  return (currentPage.value - 1) * props.pageSize;
});

const endIndex = computed(() => {
  return Math.min(startIndex.value + props.pageSize, filteredData.value.length);
});

const paginatedData = computed(() => {
  return sortedData.value.slice(startIndex.value, endIndex.value);
});

const visiblePages = computed(() => {
  const pages = [];
  const maxVisible = 5;
  const half = Math.floor(maxVisible / 2);
  
  let start = Math.max(1, currentPage.value - half);
  let end = Math.min(totalPages.value, start + maxVisible - 1);
  
  if (end - start + 1 < maxVisible) {
    start = Math.max(1, end - maxVisible + 1);
  }
  
  for (let i = start; i <= end; i++) {
    pages.push(i);
  }
  
  return pages;
});

// Methods
const getNestedValue = (obj, path) => {
  return path.split('.').reduce((current, key) => current?.[key], obj);
};

const getRowKey = (item, index) => {
  return getNestedValue(item, props.rowKey) || index;
};

const getRowClass = (item, index) => {
  // Can be extended for conditional row styling
  return '';
};

const getHeaderClass = (column) => {
  const classes = [];
  if (column.sortable) classes.push('sortable');
  if (column.align) classes.push(`text-${column.align}`);
  return classes;
};

const getCellClass = (column, item) => {
  const classes = [];
  if (column.align) classes.push(`text-${column.align}`);
  if (column.cellClass) {
    if (typeof column.cellClass === 'function') {
      classes.push(column.cellClass(item));
    } else {
      classes.push(column.cellClass);
    }
  }
  return classes;
};

const getSortIcon = (key) => {
  if (sortKey.value !== key) return 'bi bi-arrow-down-up text-muted';
  return sortOrder.value === 'asc' ? 'bi bi-arrow-up' : 'bi bi-arrow-down';
};

const formatCellValue = (item, column) => {
  const value = getNestedValue(item, column.key);
  
  if (column.format) {
    if (typeof column.format === 'function') {
      return column.format(value, item);
    }
    
    switch (column.format) {
      case 'currency':
        return new Intl.NumberFormat('en-CA', {
          style: 'currency',
          currency: 'CAD'
        }).format(value);
      case 'date':
        return new Date(value).toLocaleDateString();
      case 'datetime':
        return new Date(value).toLocaleString();
      case 'percentage':
        return `${value}%`;
      default:
        return value;
    }
  }
  
  return value;
};

const sort = (key) => {
  if (sortKey.value === key) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
  } else {
    sortKey.value = key;
    sortOrder.value = 'asc';
  }
  
  emit('sort', { key, order: sortOrder.value });
};

const goToPage = (page) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page;
  }
};

const exportData = () => {
  emit('export', filteredData.value);
};

// Watch for data changes to reset pagination
watch(() => props.data, () => {
  currentPage.value = 1;
});

watch(searchQuery, () => {
  currentPage.value = 1;
});
</script>

<style lang="scss" scoped>
.data-table {
  .table {
    th.sortable {
      cursor: pointer;
      user-select: none;
      
      &:hover {
        background-color: var(--bs-gray-100);
      }
    }
    
    tbody tr {
      transition: background-color 0.15s ease-in-out;
    }
  }
  
  .search-box {
    min-width: 200px;
  }
  
  .pagination-info {
    font-size: 0.875rem;
  }
}

// Dark theme support
[data-bs-theme="dark"] {
  .data-table {
    .table th.sortable:hover {
      background-color: var(--bs-gray-800);
    }
    
    .table-light {
      background-color: var(--bs-gray-800);
      border-color: var(--bs-border-color-translucent);
    }
  }
}
</style>
