<template>
  <div class="modal fade" :class="{ show: show }" :style="{ display: show ? 'block' : 'none' }" tabindex="-1" role="dialog" aria-labelledby="profitSubmissionModalLabel" :aria-hidden="!show">
    <div class="modal-dialog modal-lg" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="profitSubmissionModalLabel">
            <i class="bi bi-file-earmark-plus me-2"></i>
            Submit Monthly Profit
          </h5>
          <button type="button" class="btn-close" @click="closeModal" aria-label="Close"></button>
        </div>
        
        <form @submit.prevent="submitForm">
          <div class="modal-body">
            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="submissionPeriod" class="form-label">
                  Submission Period <span class="text-danger">*</span>
                </label>
                <input 
                  type="month" 
                  class="form-control" 
                  id="submissionPeriod"
                  v-model="form.submission_period"
                  :class="{ 'is-invalid': errors.submission_period }"
                  required
                >
                <div class="invalid-feedback" v-if="errors.submission_period">
                  {{ errors.submission_period }}
                </div>
                <div class="form-text">
                  Select the month for which you're submitting profit data
                </div>
              </div>
              
              <div class="col-md-6 mb-3">
                <label for="grossProfit" class="form-label">
                  Gross Profit (CAD) <span class="text-danger">*</span>
                </label>
                <div class="input-group">
                  <span class="input-group-text">$</span>
                  <input 
                    type="number" 
                    class="form-control" 
                    id="grossProfit"
                    v-model.number="form.gross_profit"
                    :class="{ 'is-invalid': errors.gross_profit }"
                    step="0.01"
                    min="0"
                    required
                  >
                  <div class="invalid-feedback" v-if="errors.gross_profit">
                    {{ errors.gross_profit }}
                  </div>
                </div>
              </div>
            </div>
            
            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="netProfit" class="form-label">
                  Net Profit (CAD) <span class="text-danger">*</span>
                </label>
                <div class="input-group">
                  <span class="input-group-text">$</span>
                  <input 
                    type="number" 
                    class="form-control" 
                    id="netProfit"
                    v-model.number="form.net_profit"
                    :class="{ 'is-invalid': errors.net_profit }"
                    step="0.01"
                    min="0"
                    required
                    @input="calculatePlainsNorthShare"
                  >
                  <div class="invalid-feedback" v-if="errors.net_profit">
                    {{ errors.net_profit }}
                  </div>
                </div>
              </div>
              
              <div class="col-md-6 mb-3">
                <label class="form-label">
                  Plains North Share (CAD)
                </label>
                <div class="input-group">
                  <span class="input-group-text">$</span>
                  <input 
                    type="text" 
                    class="form-control bg-light" 
                    :value="formatCurrency(plainsNorthShare)"
                    readonly
                  >
                </div>
                <div class="form-text">
                  Automatically calculated as 1/3 of net profit
                </div>
              </div>
            </div>
            
            <div class="mb-3">
              <label for="description" class="form-label">
                Description
              </label>
              <textarea 
                class="form-control" 
                id="description"
                v-model="form.description"
                rows="3"
                placeholder="Optional description or notes about this profit submission..."
              ></textarea>
            </div>
            
            <div class="profit-breakdown" v-if="plainsNorthShare > 0">
              <h6 class="mb-3">Distribution Breakdown</h6>
              <div class="alert alert-info">
                <div class="row">
                  <div class="col-md-4">
                    <strong>Current Stage:</strong><br>
                    <span class="badge" :class="stageBadgeClass">{{ stageLabel }}</span>
                  </div>
                  <div class="col-md-8">
                    <strong>Distribution:</strong><br>
                    <div v-if="currentStage === 'capital_recovery'">
                      <i class="bi bi-arrow-right text-primary me-1"></i>
                      100% to Capital Recovery: {{ formatCurrency(plainsNorthShare) }}
                    </div>
                    <div v-else>
                      <i class="bi bi-arrow-right text-success me-1"></i>
                      50% to Retained Share: {{ formatCurrency(plainsNorthShare / 2) }}<br>
                      <i class="bi bi-arrow-right text-info me-1"></i>
                      50% to Tokenised Pool: {{ formatCurrency(plainsNorthShare / 2) }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" @click="closeModal" :disabled="submitting">
              Cancel
            </button>
            <button type="submit" class="btn btn-primary" :disabled="submitting || !isFormValid">
              <span v-if="submitting" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
              {{ submitting ? 'Submitting...' : 'Submit Profit' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
  
  <!-- Modal backdrop -->
  <div class="modal-backdrop fade" :class="{ show: show }" v-if="show"></div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { useProfitSharingStore } from '@/stores/profitSharing';

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  currentStage: {
    type: String,
    default: 'capital_recovery'
  }
});

const emit = defineEmits(['update:show', 'submit']);

const profitSharingStore = useProfitSharingStore();

// Form data
const form = ref({
  submission_period: '',
  gross_profit: 0,
  net_profit: 0,
  description: ''
});

const errors = ref({});
const submitting = ref(false);

// Computed properties
const plainsNorthShare = computed(() => {
  return form.value.net_profit * (1/3);
});

const isFormValid = computed(() => {
  return form.value.submission_period && 
         form.value.gross_profit > 0 && 
         form.value.net_profit > 0 &&
         form.value.net_profit <= form.value.gross_profit &&
         Object.keys(errors.value).length === 0;
});

const stageBadgeClass = computed(() => {
  return props.currentStage === 'post_recovery' ? 'bg-success' : 'bg-primary';
});

const stageLabel = computed(() => {
  return props.currentStage === 'post_recovery' ? 'Post-Recovery' : 'Capital Recovery';
});

// Methods
const calculatePlainsNorthShare = () => {
  // Validation
  errors.value = {};
  
  if (form.value.net_profit < 0) {
    errors.value.net_profit = 'Net profit cannot be negative';
  }
  
  if (form.value.gross_profit < 0) {
    errors.value.gross_profit = 'Gross profit cannot be negative';
  }
  
  if (form.value.net_profit > form.value.gross_profit) {
    errors.value.net_profit = 'Net profit cannot exceed gross profit';
  }
};

const validateForm = () => {
  errors.value = {};
  
  if (!form.value.submission_period) {
    errors.value.submission_period = 'Submission period is required';
  }
  
  if (!form.value.gross_profit || form.value.gross_profit <= 0) {
    errors.value.gross_profit = 'Gross profit must be greater than 0';
  }
  
  if (!form.value.net_profit || form.value.net_profit <= 0) {
    errors.value.net_profit = 'Net profit must be greater than 0';
  }
  
  if (form.value.net_profit > form.value.gross_profit) {
    errors.value.net_profit = 'Net profit cannot exceed gross profit';
  }
  
  // Check if period is not in the future
  const selectedDate = new Date(form.value.submission_period + '-01');
  const currentDate = new Date();
  currentDate.setDate(1); // Set to first day of current month
  
  if (selectedDate > currentDate) {
    errors.value.submission_period = 'Cannot submit profit for future periods';
  }
  
  return Object.keys(errors.value).length === 0;
};

const submitForm = async () => {
  if (!validateForm()) {
    return;
  }
  
  submitting.value = true;
  
  try {
    const submissionData = {
      ...form.value,
      metadata: {
        plains_north_share: plainsNorthShare.value,
        stage: props.currentStage,
        submitted_at: new Date().toISOString()
      }
    };
    
    emit('submit', submissionData);
    resetForm();
  } catch (error) {
    console.error('Error submitting profit:', error);
    // Handle error (could show toast notification)
  } finally {
    submitting.value = false;
  }
};

const closeModal = () => {
  emit('update:show', false);
  resetForm();
};

const resetForm = () => {
  form.value = {
    submission_period: '',
    gross_profit: 0,
    net_profit: 0,
    description: ''
  };
  errors.value = {};
  submitting.value = false;
};

const formatCurrency = (amount) => {
  return new Intl.NumberFormat('en-CA', {
    style: 'currency',
    currency: 'CAD',
    minimumFractionDigits: 2
  }).format(amount);
};

// Set default submission period to last month
const setDefaultPeriod = () => {
  const now = new Date();
  const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
  const year = lastMonth.getFullYear();
  const month = String(lastMonth.getMonth() + 1).padStart(2, '0');
  form.value.submission_period = `${year}-${month}`;
};

// Watch for modal show/hide
watch(() => props.show, (newValue) => {
  if (newValue) {
    setDefaultPeriod();
    // Focus on first input when modal opens
    setTimeout(() => {
      const firstInput = document.getElementById('submissionPeriod');
      if (firstInput) firstInput.focus();
    }, 100);
  }
});

// Watch for net profit changes
watch(() => form.value.net_profit, calculatePlainsNorthShare);
watch(() => form.value.gross_profit, calculatePlainsNorthShare);
</script>

<style lang="scss" scoped>
.modal {
  &.show {
    display: block !important;
  }
}

.modal-backdrop {
  &.show {
    opacity: 0.5;
  }
}

.profit-breakdown {
  border-top: 1px solid var(--bs-border-color);
  padding-top: 1rem;
  margin-top: 1rem;
  
  .alert {
    margin-bottom: 0;
  }
}

.form-label {
  font-weight: 500;
}

.input-group-text {
  background-color: var(--bs-gray-100);
  border-color: var(--bs-border-color);
}

// Dark theme support
[data-bs-theme="dark"] {
  .input-group-text {
    background-color: var(--bs-gray-800);
    border-color: var(--bs-border-color-translucent);
    color: var(--bs-body-color);
  }
  
  .bg-light {
    background-color: var(--bs-gray-800) !important;
    color: var(--bs-body-color);
  }
}

// Animation for modal
.modal {
  transition: opacity 0.15s linear;
}

.modal-backdrop {
  transition: opacity 0.15s linear;
}
</style>
