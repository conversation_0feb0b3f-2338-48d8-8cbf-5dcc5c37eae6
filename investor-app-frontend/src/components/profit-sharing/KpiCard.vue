<template>
  <div class="kpi-card" :class="cardClass">
    <div class="card h-100">
      <div class="card-body d-flex flex-column">
        <div class="d-flex justify-content-between align-items-start mb-2">
          <div class="kpi-icon" v-if="icon">
            <i :class="icon" :style="{ color: iconColor }"></i>
          </div>
          <div class="kpi-trend" v-if="trend">
            <span class="badge" :class="trendClass">
              <i :class="trendIcon"></i>
              {{ formatTrend(trend) }}
            </span>
          </div>
        </div>
        
        <div class="kpi-content flex-grow-1">
          <h6 class="kpi-title text-muted mb-1">{{ title }}</h6>
          <div class="kpi-value mb-2">
            <span class="value-main" :class="valueClass">{{ formattedValue }}</span>
            <span class="value-unit text-muted ms-1" v-if="unit">{{ unit }}</span>
          </div>
          <p class="kpi-description text-muted small mb-0" v-if="description">
            {{ description }}
          </p>
        </div>
        
        <div class="kpi-footer mt-2" v-if="$slots.footer">
          <slot name="footer"></slot>
        </div>
      </div>
      
      <div class="card-footer bg-transparent border-0 p-2" v-if="lastUpdated">
        <small class="text-muted">
          <i class="bi bi-clock me-1"></i>
          Updated {{ formatDate(lastUpdated) }}
        </small>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  value: {
    type: [Number, String],
    required: true
  },
  unit: {
    type: String,
    default: ''
  },
  description: {
    type: String,
    default: ''
  },
  icon: {
    type: String,
    default: ''
  },
  iconColor: {
    type: String,
    default: '#6c757d'
  },
  trend: {
    type: Number,
    default: null
  },
  format: {
    type: String,
    default: 'number', // 'number', 'currency', 'percentage'
    validator: (value) => ['number', 'currency', 'percentage'].includes(value)
  },
  currency: {
    type: String,
    default: 'CAD'
  },
  variant: {
    type: String,
    default: 'default', // 'default', 'primary', 'success', 'warning', 'danger'
    validator: (value) => ['default', 'primary', 'success', 'warning', 'danger'].includes(value)
  },
  size: {
    type: String,
    default: 'medium', // 'small', 'medium', 'large'
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  lastUpdated: {
    type: [String, Date],
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const formattedValue = computed(() => {
  if (props.loading) return '...';
  
  const value = parseFloat(props.value);
  if (isNaN(value)) return props.value;

  switch (props.format) {
    case 'currency':
      return new Intl.NumberFormat('en-CA', {
        style: 'currency',
        currency: props.currency,
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
      }).format(value);
    
    case 'percentage':
      return `${value.toFixed(1)}%`;
    
    case 'number':
    default:
      if (value >= 1000000) {
        return `${(value / 1000000).toFixed(1)}M`;
      } else if (value >= 1000) {
        return `${(value / 1000).toFixed(1)}K`;
      }
      return value.toLocaleString();
  }
});

const cardClass = computed(() => {
  const classes = [`kpi-card--${props.variant}`, `kpi-card--${props.size}`];
  if (props.loading) classes.push('kpi-card--loading');
  return classes;
});

const valueClass = computed(() => {
  const classes = ['fw-bold'];
  
  switch (props.size) {
    case 'small':
      classes.push('h6');
      break;
    case 'large':
      classes.push('h3');
      break;
    case 'medium':
    default:
      classes.push('h4');
      break;
  }
  
  return classes;
});

const trendClass = computed(() => {
  if (!props.trend) return '';
  
  if (props.trend > 0) return 'bg-success';
  if (props.trend < 0) return 'bg-danger';
  return 'bg-secondary';
});

const trendIcon = computed(() => {
  if (!props.trend) return '';
  
  if (props.trend > 0) return 'bi bi-arrow-up';
  if (props.trend < 0) return 'bi bi-arrow-down';
  return 'bi bi-dash';
});

const formatTrend = (trend) => {
  if (!trend) return '';
  const abs = Math.abs(trend);
  return `${abs.toFixed(1)}%`;
};

const formatDate = (date) => {
  if (!date) return '';
  
  const d = new Date(date);
  const now = new Date();
  const diffMs = now - d;
  const diffMins = Math.floor(diffMs / 60000);
  const diffHours = Math.floor(diffMs / 3600000);
  const diffDays = Math.floor(diffMs / 86400000);
  
  if (diffMins < 1) return 'just now';
  if (diffMins < 60) return `${diffMins}m ago`;
  if (diffHours < 24) return `${diffHours}h ago`;
  if (diffDays < 7) return `${diffDays}d ago`;
  
  return d.toLocaleDateString();
};
</script>

<style lang="scss" scoped>
.kpi-card {
  .card {
    border: 1px solid var(--bs-border-color);
    transition: all 0.2s ease-in-out;
    
    &:hover {
      box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
      transform: translateY(-1px);
    }
  }
  
  .kpi-icon {
    font-size: 1.5rem;
    opacity: 0.8;
  }
  
  .kpi-title {
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
  
  .value-main {
    line-height: 1.2;
  }
  
  .kpi-trend .badge {
    font-size: 0.75rem;
    font-weight: 500;
  }
  
  // Variants
  &--primary .card {
    border-left: 4px solid var(--bs-primary);
  }
  
  &--success .card {
    border-left: 4px solid var(--bs-success);
  }
  
  &--warning .card {
    border-left: 4px solid var(--bs-warning);
  }
  
  &--danger .card {
    border-left: 4px solid var(--bs-danger);
  }
  
  // Sizes
  &--small {
    .kpi-icon {
      font-size: 1.25rem;
    }
    
    .card-body {
      padding: 0.75rem;
    }
  }
  
  &--large {
    .kpi-icon {
      font-size: 2rem;
    }
    
    .card-body {
      padding: 1.5rem;
    }
  }
  
  // Loading state
  &--loading {
    .card {
      opacity: 0.7;
    }
    
    .value-main {
      animation: pulse 1.5s ease-in-out infinite;
    }
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// Dark theme support
[data-bs-theme="dark"] {
  .kpi-card .card {
    background-color: var(--bs-dark);
    border-color: var(--bs-border-color-translucent);
    
    &:hover {
      box-shadow: 0 0.125rem 0.25rem rgba(255, 255, 255, 0.075);
    }
  }
}
</style>
