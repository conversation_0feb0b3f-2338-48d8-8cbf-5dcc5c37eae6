<template>
  <div class="kyc-panel">
    <div class="card">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
          <i class="bi bi-shield-check me-2"></i>
          KYC Verification Status
        </h5>
        <span class="badge" :class="statusBadgeClass">
          {{ statusLabel }}
        </span>
      </div>
      
      <div class="card-body">
        <div class="kyc-status-section mb-4">
          <div class="status-indicator d-flex align-items-center mb-3">
            <div class="status-icon me-3">
              <i :class="statusIcon" :style="{ color: statusColor }"></i>
            </div>
            <div class="status-content">
              <h6 class="status-title mb-1">{{ statusTitle }}</h6>
              <p class="status-description text-muted mb-0">{{ statusDescription }}</p>
            </div>
          </div>
          
          <div class="verification-details" v-if="verificationDate || rejectionReason">
            <div class="row">
              <div class="col-md-6" v-if="verificationDate">
                <div class="detail-item">
                  <label class="detail-label">Verification Date</label>
                  <div class="detail-value">{{ formatDate(verificationDate) }}</div>
                </div>
              </div>
              <div class="col-md-6" v-if="rejectionReason">
                <div class="detail-item">
                  <label class="detail-label">Rejection Reason</label>
                  <div class="detail-value text-danger">{{ rejectionReason }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="eligibility-section mb-4" v-if="showEligibility">
          <h6 class="mb-3">Distribution Eligibility</h6>
          <div class="eligibility-checks">
            <div class="check-item d-flex align-items-center mb-2">
              <i :class="verified ? 'bi bi-check-circle-fill text-success' : 'bi bi-x-circle-fill text-danger'" class="me-2"></i>
              <span>KYC Verification</span>
            </div>
            <div class="check-item d-flex align-items-center mb-2" v-if="tokenBalance !== null">
              <i :class="tokenBalance > 0 ? 'bi bi-check-circle-fill text-success' : 'bi bi-x-circle-fill text-danger'" class="me-2"></i>
              <span>Token Holdings ({{ formatTokenAmount(tokenBalance) }})</span>
            </div>
            <div class="check-item d-flex align-items-center mb-2" v-if="complianceChecks">
              <i :class="complianceChecks.all_passed ? 'bi bi-check-circle-fill text-success' : 'bi bi-x-circle-fill text-danger'" class="me-2"></i>
              <span>Compliance Checks</span>
            </div>
          </div>
          
          <div class="eligibility-result mt-3">
            <div class="alert" :class="eligibilityAlertClass" role="alert">
              <strong>{{ eligibilityTitle }}</strong>
              <div class="small">{{ eligibilityDescription }}</div>
            </div>
          </div>
        </div>
        
        <div class="action-section" v-if="showActions">
          <div class="d-flex gap-2 flex-wrap">
            <button 
              v-if="status === 'unverified' || status === 'rejected'"
              class="btn btn-primary"
              @click="$emit('initiate-kyc')"
              :disabled="loading"
            >
              <i class="bi bi-upload me-1"></i>
              {{ status === 'rejected' ? 'Re-submit' : 'Start' }} KYC Verification
            </button>
            
            <button 
              v-if="canApprove"
              class="btn btn-success"
              @click="$emit('approve-kyc')"
              :disabled="loading"
            >
              <i class="bi bi-check-lg me-1"></i>
              Approve
            </button>
            
            <button 
              v-if="canReject"
              class="btn btn-danger"
              @click="$emit('reject-kyc')"
              :disabled="loading"
            >
              <i class="bi bi-x-lg me-1"></i>
              Reject
            </button>
            
            <button 
              v-if="status === 'verified'"
              class="btn btn-outline-secondary"
              @click="$emit('view-documents')"
              :disabled="loading"
            >
              <i class="bi bi-file-earmark-text me-1"></i>
              View Documents
            </button>
          </div>
        </div>
      </div>
      
      <div class="card-footer bg-transparent" v-if="lastUpdated">
        <small class="text-muted">
          <i class="bi bi-clock me-1"></i>
          Last updated {{ formatDate(lastUpdated) }}
        </small>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  verified: {
    type: Boolean,
    default: false
  },
  status: {
    type: String,
    default: 'unverified', // 'unverified', 'pending', 'verified', 'rejected'
    validator: (value) => ['unverified', 'pending', 'verified', 'rejected'].includes(value)
  },
  verificationDate: {
    type: [String, Date],
    default: null
  },
  rejectionReason: {
    type: String,
    default: null
  },
  investorId: {
    type: [String, Number],
    required: true
  },
  tokenBalance: {
    type: Number,
    default: null
  },
  complianceChecks: {
    type: Object,
    default: null
  },
  showEligibility: {
    type: Boolean,
    default: true
  },
  showActions: {
    type: Boolean,
    default: true
  },
  canApprove: {
    type: Boolean,
    default: false
  },
  canReject: {
    type: Boolean,
    default: false
  },
  lastUpdated: {
    type: [String, Date],
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['initiate-kyc', 'approve-kyc', 'reject-kyc', 'view-documents']);

const statusBadgeClass = computed(() => {
  switch (props.status) {
    case 'verified':
      return 'bg-success text-white';
    case 'pending':
      return 'bg-warning text-dark';
    case 'rejected':
      return 'bg-danger text-white';
    case 'unverified':
    default:
      return 'bg-secondary text-white';
  }
});

const statusLabel = computed(() => {
  switch (props.status) {
    case 'verified':
      return 'Verified';
    case 'pending':
      return 'Pending Review';
    case 'rejected':
      return 'Rejected';
    case 'unverified':
    default:
      return 'Not Verified';
  }
});

const statusIcon = computed(() => {
  switch (props.status) {
    case 'verified':
      return 'bi bi-shield-check-fill';
    case 'pending':
      return 'bi bi-clock-fill';
    case 'rejected':
      return 'bi bi-shield-x-fill';
    case 'unverified':
    default:
      return 'bi bi-shield-exclamation-fill';
  }
});

const statusColor = computed(() => {
  switch (props.status) {
    case 'verified':
      return '#198754'; // success
    case 'pending':
      return '#ffc107'; // warning
    case 'rejected':
      return '#dc3545'; // danger
    case 'unverified':
    default:
      return '#6c757d'; // secondary
  }
});

const statusTitle = computed(() => {
  switch (props.status) {
    case 'verified':
      return 'KYC Verification Complete';
    case 'pending':
      return 'KYC Under Review';
    case 'rejected':
      return 'KYC Verification Rejected';
    case 'unverified':
    default:
      return 'KYC Verification Required';
  }
});

const statusDescription = computed(() => {
  switch (props.status) {
    case 'verified':
      return 'Your identity has been verified and you are eligible for profit distributions.';
    case 'pending':
      return 'Your KYC documents are being reviewed. This process typically takes 1-3 business days.';
    case 'rejected':
      return 'Your KYC verification was rejected. Please review the reason and resubmit with corrected information.';
    case 'unverified':
    default:
      return 'Complete KYC verification to become eligible for profit distributions and other platform features.';
  }
});

const isEligible = computed(() => {
  const kycVerified = props.status === 'verified';
  const hasTokens = props.tokenBalance > 0;
  const compliancePassed = props.complianceChecks?.all_passed !== false;
  
  return kycVerified && hasTokens && compliancePassed;
});

const eligibilityAlertClass = computed(() => {
  return isEligible.value ? 'alert-success' : 'alert-warning';
});

const eligibilityTitle = computed(() => {
  return isEligible.value ? 'Eligible for Distributions' : 'Not Eligible for Distributions';
});

const eligibilityDescription = computed(() => {
  if (isEligible.value) {
    return 'You meet all requirements and are eligible to receive profit distributions.';
  }
  
  const issues = [];
  if (props.status !== 'verified') issues.push('KYC verification');
  if (props.tokenBalance <= 0) issues.push('token holdings');
  if (props.complianceChecks && !props.complianceChecks.all_passed) issues.push('compliance checks');
  
  return `Complete the following requirements: ${issues.join(', ')}.`;
});

const formatDate = (date) => {
  if (!date) return '';
  
  const d = new Date(date);
  return d.toLocaleDateString('en-CA', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

const formatTokenAmount = (amount) => {
  if (amount === null || amount === undefined) return 'N/A';
  
  if (amount >= 1000000) {
    return `${(amount / 1000000).toFixed(1)}M`;
  } else if (amount >= 1000) {
    return `${(amount / 1000).toFixed(1)}K`;
  }
  
  return amount.toLocaleString();
};
</script>

<style lang="scss" scoped>
.kyc-panel {
  .card {
    border: 1px solid var(--bs-border-color);
  }
  
  .status-icon {
    font-size: 2rem;
  }
  
  .status-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
  }
  
  .detail-item {
    margin-bottom: 1rem;
    
    .detail-label {
      font-size: 0.875rem;
      font-weight: 500;
      color: var(--bs-secondary);
      margin-bottom: 0.25rem;
      display: block;
    }
    
    .detail-value {
      font-weight: 500;
    }
  }
  
  .check-item {
    font-size: 0.9rem;
    
    i {
      font-size: 1rem;
    }
  }
  
  .eligibility-result .alert {
    margin-bottom: 0;
  }
}

// Dark theme support
[data-bs-theme="dark"] {
  .kyc-panel {
    .card {
      background-color: var(--bs-dark);
      border-color: var(--bs-border-color-translucent);
    }
  }
}
</style>
