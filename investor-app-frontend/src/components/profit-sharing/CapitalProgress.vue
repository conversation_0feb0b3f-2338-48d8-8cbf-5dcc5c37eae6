<template>
  <div class="capital-progress">
    <div class="card">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
          <i class="bi bi-piggy-bank me-2 text-primary"></i>
          Capital Recovery Progress
        </h5>
        <span class="badge" :class="stageBadgeClass">
          {{ stageLabel }}
        </span>
      </div>
      
      <div class="card-body">
        <div class="progress-section mb-4">
          <div class="d-flex justify-content-between align-items-center mb-2">
            <span class="progress-label">Recovery Progress</span>
            <span class="progress-percentage fw-bold">{{ formattedPercentage }}</span>
          </div>
          
          <div class="progress progress-lg mb-3" style="height: 12px;">
            <div 
              class="progress-bar" 
              :class="progressBarClass"
              role="progressbar" 
              :style="{ width: `${percentage}%` }"
              :aria-valuenow="percentage" 
              aria-valuemin="0" 
              aria-valuemax="100"
            >
              <span class="visually-hidden">{{ formattedPercentage }} complete</span>
            </div>
          </div>
          
          <div class="progress-details">
            <div class="row text-center">
              <div class="col-4">
                <div class="detail-item">
                  <div class="detail-value text-success fw-bold">
                    {{ formatCurrency(repaidAmount) }}
                  </div>
                  <div class="detail-label text-muted small">Repaid</div>
                </div>
              </div>
              <div class="col-4">
                <div class="detail-item">
                  <div class="detail-value text-warning fw-bold">
                    {{ formatCurrency(remainingAmount) }}
                  </div>
                  <div class="detail-label text-muted small">Remaining</div>
                </div>
              </div>
              <div class="col-4">
                <div class="detail-item">
                  <div class="detail-value text-primary fw-bold">
                    {{ formatCurrency(targetAmount) }}
                  </div>
                  <div class="detail-label text-muted small">Target</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="stage-info" v-if="stage">
          <div class="alert" :class="stageAlertClass" role="alert">
            <div class="d-flex align-items-center">
              <i :class="stageIcon" class="me-2"></i>
              <div>
                <strong>{{ stageTitle }}</strong>
                <div class="small">{{ stageDescription }}</div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="timeline-section" v-if="showTimeline && milestones.length > 0">
          <h6 class="mb-3">Recovery Timeline</h6>
          <div class="timeline">
            <div 
              v-for="(milestone, index) in milestones" 
              :key="index"
              class="timeline-item"
              :class="{ 'timeline-item--completed': milestone.completed }"
            >
              <div class="timeline-marker">
                <i :class="milestone.completed ? 'bi bi-check-circle-fill' : 'bi bi-circle'"></i>
              </div>
              <div class="timeline-content">
                <div class="timeline-title">{{ milestone.title }}</div>
                <div class="timeline-amount">{{ formatCurrency(milestone.amount) }}</div>
                <div class="timeline-date text-muted small" v-if="milestone.date">
                  {{ formatDate(milestone.date) }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="card-footer bg-transparent" v-if="lastUpdated">
        <small class="text-muted">
          <i class="bi bi-clock me-1"></i>
          Last updated {{ formatDate(lastUpdated) }}
        </small>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  targetAmount: {
    type: Number,
    default: 150000
  },
  repaidAmount: {
    type: Number,
    default: 0
  },
  remainingAmount: {
    type: Number,
    default: 150000
  },
  stage: {
    type: String,
    default: 'capital_recovery',
    validator: (value) => ['capital_recovery', 'post_recovery'].includes(value)
  },
  currency: {
    type: String,
    default: 'CAD'
  },
  showTimeline: {
    type: Boolean,
    default: false
  },
  milestones: {
    type: Array,
    default: () => []
  },
  lastUpdated: {
    type: [String, Date],
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const percentage = computed(() => {
  if (props.targetAmount === 0) return 0;
  return Math.min(100, (props.repaidAmount / props.targetAmount) * 100);
});

const formattedPercentage = computed(() => {
  return `${percentage.value.toFixed(1)}%`;
});

const progressBarClass = computed(() => {
  if (percentage.value >= 100) return 'bg-success';
  if (percentage.value >= 75) return 'bg-info';
  if (percentage.value >= 50) return 'bg-warning';
  return 'bg-primary';
});

const stageBadgeClass = computed(() => {
  switch (props.stage) {
    case 'post_recovery':
      return 'bg-success text-white';
    case 'capital_recovery':
    default:
      return 'bg-primary text-white';
  }
});

const stageLabel = computed(() => {
  switch (props.stage) {
    case 'post_recovery':
      return 'Post-Recovery';
    case 'capital_recovery':
    default:
      return 'Capital Recovery';
  }
});

const stageAlertClass = computed(() => {
  switch (props.stage) {
    case 'post_recovery':
      return 'alert-success';
    case 'capital_recovery':
    default:
      return 'alert-info';
  }
});

const stageIcon = computed(() => {
  switch (props.stage) {
    case 'post_recovery':
      return 'bi bi-check-circle-fill';
    case 'capital_recovery':
    default:
      return 'bi bi-arrow-repeat';
  }
});

const stageTitle = computed(() => {
  switch (props.stage) {
    case 'post_recovery':
      return 'Capital Recovery Complete';
    case 'capital_recovery':
    default:
      return 'Capital Recovery in Progress';
  }
});

const stageDescription = computed(() => {
  switch (props.stage) {
    case 'post_recovery':
      return 'All capital has been recovered. Profits are now split 50/50 between retained share and tokenised profit pool.';
    case 'capital_recovery':
    default:
      return '100% of Plains North\'s profit share goes toward capital recovery until the target is reached.';
  }
});

const formatCurrency = (amount) => {
  if (props.loading) return '...';
  
  return new Intl.NumberFormat('en-CA', {
    style: 'currency',
    currency: props.currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
};

const formatDate = (date) => {
  if (!date) return '';
  
  const d = new Date(date);
  const now = new Date();
  const diffMs = now - d;
  const diffDays = Math.floor(diffMs / 86400000);
  
  if (diffDays === 0) return 'Today';
  if (diffDays === 1) return 'Yesterday';
  if (diffDays < 7) return `${diffDays} days ago`;
  
  return d.toLocaleDateString('en-CA', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};
</script>

<style lang="scss" scoped>
.capital-progress {
  .card {
    border: 1px solid var(--bs-border-color);
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  }
  
  .progress-lg {
    border-radius: 6px;
    overflow: hidden;
  }
  
  .detail-item {
    padding: 0.5rem;
    
    .detail-value {
      font-size: 1.1rem;
      line-height: 1.2;
    }
    
    .detail-label {
      font-size: 0.75rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      margin-top: 0.25rem;
    }
  }
  
  .timeline {
    position: relative;
    padding-left: 1.5rem;
    
    &::before {
      content: '';
      position: absolute;
      left: 0.5rem;
      top: 0;
      bottom: 0;
      width: 2px;
      background-color: var(--bs-border-color);
    }
  }
  
  .timeline-item {
    position: relative;
    padding-bottom: 1.5rem;
    
    &:last-child {
      padding-bottom: 0;
    }
    
    .timeline-marker {
      position: absolute;
      left: -1.25rem;
      top: 0.25rem;
      width: 1rem;
      height: 1rem;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--bs-body-bg);
      
      i {
        font-size: 1rem;
        color: var(--bs-secondary);
      }
    }
    
    &.timeline-item--completed .timeline-marker i {
      color: var(--bs-success);
    }
    
    .timeline-content {
      .timeline-title {
        font-weight: 500;
        margin-bottom: 0.25rem;
      }
      
      .timeline-amount {
        font-weight: 600;
        color: var(--bs-primary);
        margin-bottom: 0.25rem;
      }
    }
  }
}

// Dark theme support
[data-bs-theme="dark"] {
  .capital-progress {
    .card {
      background-color: var(--bs-dark);
      border-color: var(--bs-border-color-translucent);
    }
    
    .timeline::before {
      background-color: var(--bs-border-color-translucent);
    }
    
    .timeline-marker {
      background-color: var(--bs-dark);
    }
  }
}

// Loading animation
.capital-progress.loading {
  .progress-bar {
    animation: progress-loading 2s ease-in-out infinite;
  }
}

@keyframes progress-loading {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>
