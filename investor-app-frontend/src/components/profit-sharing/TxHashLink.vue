<template>
  <div class="tx-hash-link" v-if="txHash">
    <div class="d-flex align-items-center gap-2">
      <span class="tx-hash-display" :class="displayClass">
        {{ displayHash }}
      </span>
      
      <div class="tx-actions d-flex gap-1">
        <button 
          class="btn btn-sm btn-outline-secondary"
          @click="copyToClipboard"
          :title="copyTooltip"
          :disabled="copying"
        >
          <i :class="copyIcon"></i>
        </button>
        
        <a 
          :href="explorerUrl" 
          target="_blank" 
          rel="noopener noreferrer"
          class="btn btn-sm btn-outline-primary"
          title="View on Block Explorer"
          v-if="explorerUrl"
        >
          <i class="bi bi-box-arrow-up-right"></i>
        </a>
      </div>
    </div>
    
    <div class="tx-status mt-1" v-if="showStatus">
      <span class="badge" :class="statusBadgeClass">
        <i :class="statusIcon" class="me-1"></i>
        {{ statusLabel }}
      </span>
      
      <span class="text-muted small ms-2" v-if="confirmations !== null">
        {{ confirmations }} confirmations
      </span>
    </div>
    
    <div class="tx-details mt-2" v-if="showDetails && (blockNumber || timestamp)">
      <div class="row">
        <div class="col-sm-6" v-if="blockNumber">
          <small class="text-muted">
            <strong>Block:</strong> {{ blockNumber.toLocaleString() }}
          </small>
        </div>
        <div class="col-sm-6" v-if="timestamp">
          <small class="text-muted">
            <strong>Time:</strong> {{ formatTimestamp(timestamp) }}
          </small>
        </div>
      </div>
    </div>
  </div>
  
  <div class="tx-hash-link tx-hash-empty" v-else>
    <span class="text-muted">
      <i class="bi bi-dash-circle me-1"></i>
      {{ emptyText }}
    </span>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue';

const props = defineProps({
  txHash: {
    type: String,
    default: ''
  },
  network: {
    type: String,
    default: 'ethereum', // 'ethereum', 'polygon', 'bsc', 'arbitrum', etc.
    validator: (value) => ['ethereum', 'polygon', 'bsc', 'arbitrum', 'optimism'].includes(value)
  },
  status: {
    type: String,
    default: 'confirmed', // 'pending', 'confirmed', 'failed'
    validator: (value) => ['pending', 'confirmed', 'failed'].includes(value)
  },
  confirmations: {
    type: Number,
    default: null
  },
  blockNumber: {
    type: Number,
    default: null
  },
  timestamp: {
    type: [String, Date, Number],
    default: null
  },
  truncate: {
    type: Boolean,
    default: true
  },
  truncateLength: {
    type: Number,
    default: 8
  },
  showStatus: {
    type: Boolean,
    default: false
  },
  showDetails: {
    type: Boolean,
    default: false
  },
  emptyText: {
    type: String,
    default: 'No transaction hash'
  },
  customExplorerUrl: {
    type: String,
    default: ''
  }
});

// Reactive data
const copying = ref(false);
const copySuccess = ref(false);

// Computed properties
const displayHash = computed(() => {
  if (!props.txHash) return '';
  
  if (props.truncate && props.txHash.length > props.truncateLength * 2) {
    const start = props.txHash.slice(0, props.truncateLength);
    const end = props.txHash.slice(-props.truncateLength);
    return `${start}...${end}`;
  }
  
  return props.txHash;
});

const displayClass = computed(() => {
  const classes = ['font-monospace'];
  
  if (props.truncate) {
    classes.push('text-truncate');
  }
  
  return classes;
});

const explorerUrl = computed(() => {
  if (props.customExplorerUrl) {
    return props.customExplorerUrl.replace('{hash}', props.txHash);
  }
  
  if (!props.txHash) return '';
  
  const baseUrls = {
    ethereum: 'https://etherscan.io/tx/',
    polygon: 'https://polygonscan.com/tx/',
    bsc: 'https://bscscan.com/tx/',
    arbitrum: 'https://arbiscan.io/tx/',
    optimism: 'https://optimistic.etherscan.io/tx/'
  };
  
  const baseUrl = baseUrls[props.network] || baseUrls.ethereum;
  return `${baseUrl}${props.txHash}`;
});

const statusBadgeClass = computed(() => {
  switch (props.status) {
    case 'confirmed':
      return 'bg-success text-white';
    case 'pending':
      return 'bg-warning text-dark';
    case 'failed':
      return 'bg-danger text-white';
    default:
      return 'bg-secondary text-white';
  }
});

const statusIcon = computed(() => {
  switch (props.status) {
    case 'confirmed':
      return 'bi bi-check-circle-fill';
    case 'pending':
      return 'bi bi-clock-fill';
    case 'failed':
      return 'bi bi-x-circle-fill';
    default:
      return 'bi bi-question-circle-fill';
  }
});

const statusLabel = computed(() => {
  switch (props.status) {
    case 'confirmed':
      return 'Confirmed';
    case 'pending':
      return 'Pending';
    case 'failed':
      return 'Failed';
    default:
      return 'Unknown';
  }
});

const copyIcon = computed(() => {
  if (copying.value) return 'bi bi-hourglass-split';
  if (copySuccess.value) return 'bi bi-check-lg';
  return 'bi bi-clipboard';
});

const copyTooltip = computed(() => {
  if (copying.value) return 'Copying...';
  if (copySuccess.value) return 'Copied!';
  return 'Copy transaction hash';
});

// Methods
const copyToClipboard = async () => {
  if (!props.txHash || copying.value) return;
  
  copying.value = true;
  
  try {
    await navigator.clipboard.writeText(props.txHash);
    copySuccess.value = true;
    
    setTimeout(() => {
      copySuccess.value = false;
    }, 2000);
  } catch (error) {
    console.error('Failed to copy to clipboard:', error);
    
    // Fallback for older browsers
    try {
      const textArea = document.createElement('textarea');
      textArea.value = props.txHash;
      textArea.style.position = 'fixed';
      textArea.style.opacity = '0';
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      
      copySuccess.value = true;
      setTimeout(() => {
        copySuccess.value = false;
      }, 2000);
    } catch (fallbackError) {
      console.error('Fallback copy failed:', fallbackError);
    }
  } finally {
    copying.value = false;
  }
};

const formatTimestamp = (timestamp) => {
  if (!timestamp) return '';
  
  const date = new Date(timestamp);
  const now = new Date();
  const diffMs = now - date;
  const diffMins = Math.floor(diffMs / 60000);
  const diffHours = Math.floor(diffMs / 3600000);
  const diffDays = Math.floor(diffMs / 86400000);
  
  if (diffMins < 1) return 'just now';
  if (diffMins < 60) return `${diffMins}m ago`;
  if (diffHours < 24) return `${diffHours}h ago`;
  if (diffDays < 7) return `${diffDays}d ago`;
  
  return date.toLocaleDateString('en-CA', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};
</script>

<style lang="scss" scoped>
.tx-hash-link {
  .tx-hash-display {
    font-size: 0.875rem;
    max-width: 200px;
    
    &.text-truncate {
      display: inline-block;
    }
  }
  
  .tx-actions {
    .btn {
      padding: 0.25rem 0.5rem;
      font-size: 0.75rem;
      line-height: 1;
      
      i {
        font-size: 0.875rem;
      }
    }
  }
  
  .tx-status {
    .badge {
      font-size: 0.75rem;
      
      i {
        font-size: 0.75rem;
      }
    }
  }
  
  .tx-details {
    font-size: 0.75rem;
    
    .row > div {
      margin-bottom: 0.25rem;
    }
  }
  
  &.tx-hash-empty {
    font-size: 0.875rem;
    opacity: 0.7;
  }
}

// Responsive adjustments
@media (max-width: 576px) {
  .tx-hash-link {
    .tx-hash-display {
      max-width: 120px;
    }
    
    .tx-details .row {
      flex-direction: column;
      
      .col-sm-6 {
        width: 100%;
      }
    }
  }
}

// Dark theme support
[data-bs-theme="dark"] {
  .tx-hash-link {
    .btn-outline-secondary {
      border-color: var(--bs-border-color-translucent);
      
      &:hover {
        background-color: var(--bs-secondary);
        border-color: var(--bs-secondary);
      }
    }
    
    .btn-outline-primary {
      border-color: var(--bs-border-color-translucent);
      
      &:hover {
        background-color: var(--bs-primary);
        border-color: var(--bs-primary);
      }
    }
  }
}

// Animation for copy success
.tx-hash-link .tx-actions .btn {
  transition: all 0.2s ease-in-out;
  
  &:active {
    transform: scale(0.95);
  }
}
</style>
