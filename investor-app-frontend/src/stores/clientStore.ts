import { defineStore } from 'pinia';

export const useClientStore = defineStore('client', {
  state: () => ({
    clientCount: null as number | null,
  }),

  getters: {
    effectiveClientCount(state) {
      if (state.clientCount !== null && state.clientCount !== undefined) {
        return state.clientCount;
      } else {
        return 0; // default value
      }
    },
  },

  actions: {
    async fetchClientCount() {
      // Implement API call to get clientCount, update the state.clientCount
    }
  }
});
