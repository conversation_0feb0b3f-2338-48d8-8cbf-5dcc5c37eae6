import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { profitSharingApi } from '@/api/profitSharing';

export const useProfitSharingStore = defineStore('profitSharing', () => {
  // State
  const capitalStatus = ref(null);
  const tokenHoldings = ref([]);
  const payoutHistory = ref([]);
  const distributions = ref([]);
  const profitSubmissions = ref([]);
  const statistics = ref({});
  const blockchainHealth = ref({});
  const eventListenerHealth = ref({});
  const complianceChecks = ref(null);
  const loading = ref(false);
  const error = ref(null);

  // Getters
  const isCapitalRecoveryComplete = computed(() => {
    return capitalStatus.value?.stage === 'post_recovery';
  });

  const recoveryPercentage = computed(() => {
    return capitalStatus.value?.recovery_percentage || 0;
  });

  const totalTokenBalance = computed(() => {
    return tokenHoldings.value.reduce((sum, holding) => 
      sum + parseFloat(holding.balance || 0), 0);
  });

  const totalReceived = computed(() => {
    return payoutHistory.value.reduce((sum, payout) => 
      sum + parseFloat(payout.amount || 0), 0);
  });

  const pendingSubmissions = computed(() => {
    return profitSubmissions.value.filter(submission => 
      submission.status === 'pending');
  });

  const recentDistributions = computed(() => {
    return distributions.value.slice(0, 10);
  });

  // Actions
  const setLoading = (isLoading) => {
    loading.value = isLoading;
  };

  const setError = (errorMessage) => {
    error.value = errorMessage;
  };

  const clearError = () => {
    error.value = null;
  };

  // Capital Recovery Actions
  const fetchCapitalStatus = async () => {
    try {
      setLoading(true);
      clearError();
      
      const response = await profitSharingApi.getCapitalStatus();
      capitalStatus.value = response.data;
      
      return response.data;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateCapitalRecovery = async (amount, txHash = null) => {
    try {
      setLoading(true);
      clearError();
      
      const response = await profitSharingApi.updateCapitalRecovery(amount, txHash);
      
      // Refresh capital status
      await fetchCapitalStatus();
      
      return response.data;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const fetchCapitalHistory = async (limit = 50, offset = 0) => {
    try {
      const response = await profitSharingApi.getCapitalHistory(limit, offset);
      return response.data;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  const fetchCapitalStatistics = async () => {
    try {
      const response = await profitSharingApi.getCapitalStatistics();
      return response.data;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  // Token Holdings Actions
  const fetchTokenHoldings = async (investorId) => {
    try {
      setLoading(true);
      clearError();
      
      const response = await profitSharingApi.getTokenBalance(investorId);
      tokenHoldings.value = response.data;
      
      return response.data;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const syncTokenHoldings = async () => {
    try {
      setLoading(true);
      clearError();
      
      const response = await profitSharingApi.syncTokenHoldings();
      
      return response.data;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Payout History Actions
  const fetchPayoutHistory = async (investorId, limit = 50, offset = 0) => {
    try {
      setLoading(true);
      clearError();
      
      const response = await profitSharingApi.getPayoutHistory(investorId, limit, offset);
      payoutHistory.value = response.data;
      
      return response.data;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Profit Submission Actions
  const submitProfit = async (profitData) => {
    try {
      setLoading(true);
      clearError();
      
      const response = await profitSharingApi.submitProfit(profitData);
      
      // Refresh profit submissions
      await fetchProfitSubmissions();
      
      return response.data;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const approveProfitSubmission = async (submissionId) => {
    try {
      setLoading(true);
      clearError();
      
      const response = await profitSharingApi.approveProfitSubmission(submissionId);
      
      // Refresh data
      await Promise.all([
        fetchProfitSubmissions(),
        fetchDistributions(),
        fetchCapitalStatus()
      ]);
      
      return response.data;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const rejectProfitSubmission = async (submissionId, reason = '') => {
    try {
      setLoading(true);
      clearError();
      
      const response = await profitSharingApi.rejectProfitSubmission(submissionId, reason);
      
      // Refresh profit submissions
      await fetchProfitSubmissions();
      
      return response.data;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const fetchProfitSubmissions = async (limit = 50, offset = 0) => {
    try {
      const response = await profitSharingApi.getProfitSubmissions(limit, offset);
      profitSubmissions.value = response.data;
      
      return response.data;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  // Distribution Actions
  const fetchDistributions = async (limit = 50, offset = 0) => {
    try {
      const response = await profitSharingApi.getDistributions(limit, offset);
      distributions.value = response.data;
      
      return response.data;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  const fetchDistributionDetails = async (distributionId) => {
    try {
      const response = await profitSharingApi.getDistributionDetails(distributionId);
      return response.data;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  const fetchDistributionPayouts = async (distributionId) => {
    try {
      const response = await profitSharingApi.getDistributionPayouts(distributionId);
      return response.data;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  // Dashboard Actions
  const fetchInvestorDashboard = async (investorId) => {
    try {
      setLoading(true);
      clearError();
      
      const response = await profitSharingApi.getInvestorDashboard(investorId);
      
      // Update relevant state
      if (response.data.capital_status) {
        capitalStatus.value = response.data.capital_status;
      }
      if (response.data.token_balances) {
        tokenHoldings.value = response.data.token_balances;
      }
      if (response.data.recent_payouts) {
        payoutHistory.value = response.data.recent_payouts;
      }
      
      return response.data;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const fetchAdminDashboard = async () => {
    try {
      setLoading(true);
      clearError();
      
      const response = await profitSharingApi.getAdminDashboard();
      
      // Update relevant state
      if (response.data.capital_status) {
        capitalStatus.value = response.data.capital_status;
      }
      if (response.data.recent_distributions) {
        distributions.value = response.data.recent_distributions;
      }
      if (response.data.recent_profit_submissions) {
        profitSubmissions.value = response.data.recent_profit_submissions;
      }
      if (response.data.statistics) {
        statistics.value = response.data.statistics;
      }
      
      return response.data;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Statistics Actions
  const fetchStatistics = async () => {
    try {
      const response = await profitSharingApi.getStatistics();
      statistics.value = response.data;
      
      return response.data;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  // System Health Actions
  const fetchSystemHealth = async () => {
    try {
      const response = await profitSharingApi.getSystemHealth();
      
      if (response.data.blockchain_service) {
        blockchainHealth.value = response.data.blockchain_service;
      }
      if (response.data.event_listener) {
        eventListenerHealth.value = response.data.event_listener;
      }
      
      return response.data;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  const restartEventListener = async () => {
    try {
      setLoading(true);
      clearError();
      
      const response = await profitSharingApi.restartEventListener();
      
      // Refresh system health
      await fetchSystemHealth();
      
      return response.data;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // KYC Actions
  const updateKycStatus = async (investorId, verified) => {
    try {
      setLoading(true);
      clearError();
      
      const response = await profitSharingApi.updateKycStatus(investorId, verified);
      
      return response.data;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const checkDistributionEligibility = async (investorId) => {
    try {
      const response = await profitSharingApi.checkDistributionEligibility(investorId);
      complianceChecks.value = response.data;
      
      return response.data;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  // Utility Actions
  const exportData = (data, filename) => {
    try {
      const csv = convertToCSV(data);
      downloadCSV(csv, `${filename}-${new Date().toISOString().split('T')[0]}.csv`);
    } catch (err) {
      setError('Failed to export data');
      throw err;
    }
  };

  const convertToCSV = (data) => {
    if (!data || data.length === 0) return '';
    
    const headers = Object.keys(data[0]);
    const csvHeaders = headers.join(',');
    
    const csvRows = data.map(row => {
      return headers.map(header => {
        const value = row[header];
        // Escape commas and quotes in CSV
        if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value;
      }).join(',');
    });
    
    return [csvHeaders, ...csvRows].join('\n');
  };

  const downloadCSV = (csv, filename) => {
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', filename);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  // Reset store
  const $reset = () => {
    capitalStatus.value = null;
    tokenHoldings.value = [];
    payoutHistory.value = [];
    distributions.value = [];
    profitSubmissions.value = [];
    statistics.value = {};
    blockchainHealth.value = {};
    eventListenerHealth.value = {};
    complianceChecks.value = null;
    loading.value = false;
    error.value = null;
  };

  return {
    // State
    capitalStatus,
    tokenHoldings,
    payoutHistory,
    distributions,
    profitSubmissions,
    statistics,
    blockchainHealth,
    eventListenerHealth,
    complianceChecks,
    loading,
    error,
    
    // Getters
    isCapitalRecoveryComplete,
    recoveryPercentage,
    totalTokenBalance,
    totalReceived,
    pendingSubmissions,
    recentDistributions,
    
    // Actions
    setLoading,
    setError,
    clearError,
    fetchCapitalStatus,
    updateCapitalRecovery,
    fetchCapitalHistory,
    fetchCapitalStatistics,
    fetchTokenHoldings,
    syncTokenHoldings,
    fetchPayoutHistory,
    submitProfit,
    approveProfitSubmission,
    rejectProfitSubmission,
    fetchProfitSubmissions,
    fetchDistributions,
    fetchDistributionDetails,
    fetchDistributionPayouts,
    fetchInvestorDashboard,
    fetchAdminDashboard,
    fetchStatistics,
    fetchSystemHealth,
    restartEventListener,
    updateKycStatus,
    checkDistributionEligibility,
    exportData,
    $reset
  };
});
