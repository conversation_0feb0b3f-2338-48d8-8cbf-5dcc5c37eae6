#!/usr/bin/env node

/**
 * Comprehensive Test Runner for Profit Sharing System
 * 
 * This script runs all tests for the profit sharing implementation:
 * - Backend integration tests
 * - Frontend component tests
 * - Store tests
 * - End-to-end tests
 * 
 * Usage:
 *   node test-runner.js [options]
 * 
 * Options:
 *   --backend-only    Run only backend tests
 *   --frontend-only   Run only frontend tests
 *   --e2e-only        Run only end-to-end tests
 *   --unit-only       Run only unit tests (no e2e)
 *   --watch           Run tests in watch mode
 *   --coverage        Generate coverage reports
 *   --verbose         Verbose output
 *   --help            Show this help message
 */

import { spawn } from 'child_process';
import { existsSync } from 'fs';
import { join } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Configuration
const config = {
  backendDir: join(__dirname, 'investor-app-backend'),
  frontendDir: join(__dirname, 'investor-app-frontend'),
  testTimeout: 30000,
  e2eTimeout: 120000
};

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  backendOnly: args.includes('--backend-only'),
  frontendOnly: args.includes('--frontend-only'),
  e2eOnly: args.includes('--e2e-only'),
  unitOnly: args.includes('--unit-only'),
  watch: args.includes('--watch'),
  coverage: args.includes('--coverage'),
  verbose: args.includes('--verbose'),
  help: args.includes('--help')
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function showHelp() {
  log('Profit Sharing System Test Runner', 'bright');
  log('');
  log('Usage: node test-runner.js [options]', 'cyan');
  log('');
  log('Options:', 'yellow');
  log('  --backend-only    Run only backend tests');
  log('  --frontend-only   Run only frontend tests');
  log('  --e2e-only        Run only end-to-end tests');
  log('  --unit-only       Run only unit tests (no e2e)');
  log('  --watch           Run tests in watch mode');
  log('  --coverage        Generate coverage reports');
  log('  --verbose         Verbose output');
  log('  --help            Show this help message');
  log('');
  log('Examples:', 'green');
  log('  node test-runner.js                    # Run all tests');
  log('  node test-runner.js --backend-only     # Backend tests only');
  log('  node test-runner.js --coverage         # Run with coverage');
  log('  node test-runner.js --watch --unit-only # Watch unit tests');
}

async function runCommand(command, args, cwd, description) {
  return new Promise((resolve, reject) => {
    log(`\n${colors.cyan}▶ ${description}${colors.reset}`);
    
    if (options.verbose) {
      log(`Running: ${command} ${args.join(' ')} in ${cwd}`, 'blue');
    }
    
    const child = spawn(command, args, {
      cwd,
      stdio: 'inherit',
      shell: true
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        log(`✅ ${description} - PASSED`, 'green');
        resolve(code);
      } else {
        log(`❌ ${description} - FAILED (exit code: ${code})`, 'red');
        reject(new Error(`${description} failed with exit code ${code}`));
      }
    });
    
    child.on('error', (error) => {
      log(`❌ ${description} - ERROR: ${error.message}`, 'red');
      reject(error);
    });
  });
}

async function checkPrerequisites() {
  log('🔍 Checking prerequisites...', 'yellow');
  
  // Check if directories exist
  if (!existsSync(config.backendDir)) {
    throw new Error(`Backend directory not found: ${config.backendDir}`);
  }
  
  if (!existsSync(config.frontendDir)) {
    throw new Error(`Frontend directory not found: ${config.frontendDir}`);
  }
  
  // Check if package.json files exist
  const backendPackageJson = join(config.backendDir, 'package.json');
  const frontendPackageJson = join(config.frontendDir, 'package.json');
  
  if (!existsSync(backendPackageJson)) {
    throw new Error(`Backend package.json not found: ${backendPackageJson}`);
  }
  
  if (!existsSync(frontendPackageJson)) {
    throw new Error(`Frontend package.json not found: ${frontendPackageJson}`);
  }
  
  log('✅ Prerequisites check passed', 'green');
}

async function runBackendTests() {
  log('\n🚀 Running Backend Tests', 'magenta');
  
  const testArgs = ['test'];
  
  if (options.watch) {
    testArgs.push('--watch');
  }
  
  if (options.coverage) {
    testArgs.push('--coverage');
  }
  
  if (options.verbose) {
    testArgs.push('--verbose');
  }
  
  // Add specific test pattern for profit sharing
  testArgs.push('--testPathPattern=profitSharing');
  
  await runCommand(
    'npm',
    testArgs,
    config.backendDir,
    'Backend Integration Tests'
  );
}

async function runFrontendTests() {
  log('\n🎨 Running Frontend Tests', 'magenta');
  
  const testArgs = ['run', 'test'];
  
  if (options.watch) {
    testArgs.push('--watch');
  }
  
  if (options.coverage) {
    testArgs.push('--coverage');
  }
  
  // Run component tests
  await runCommand(
    'npm',
    [...testArgs, '--', 'tests/components/profit-sharing/'],
    config.frontendDir,
    'Frontend Component Tests'
  );
  
  // Run store tests
  await runCommand(
    'npm',
    [...testArgs, '--', 'tests/stores/profitSharing.test.js'],
    config.frontendDir,
    'Frontend Store Tests'
  );
}

async function runE2ETests() {
  log('\n🌐 Running End-to-End Tests', 'magenta');
  
  // Check if Playwright is installed
  try {
    await runCommand(
      'npx',
      ['playwright', '--version'],
      config.frontendDir,
      'Checking Playwright installation'
    );
  } catch (error) {
    log('Installing Playwright...', 'yellow');
    await runCommand(
      'npx',
      ['playwright', 'install'],
      config.frontendDir,
      'Installing Playwright browsers'
    );
  }
  
  const e2eArgs = ['playwright', 'test'];
  
  if (options.verbose) {
    e2eArgs.push('--reporter=verbose');
  }
  
  // Add specific test file
  e2eArgs.push('tests/e2e/profitSharingFlow.test.js');
  
  await runCommand(
    'npx',
    e2eArgs,
    config.frontendDir,
    'End-to-End Tests'
  );
}

async function generateTestReport() {
  log('\n📊 Generating Test Report', 'cyan');
  
  const reportData = {
    timestamp: new Date().toISOString(),
    testSuites: [],
    summary: {
      total: 0,
      passed: 0,
      failed: 0,
      coverage: null
    }
  };
  
  // This would collect actual test results in a real implementation
  log('Test report would be generated here', 'blue');
  log('Report data structure:', 'blue');
  console.log(JSON.stringify(reportData, null, 2));
}

async function main() {
  try {
    if (options.help) {
      showHelp();
      return;
    }
    
    log('🧪 Profit Sharing System Test Runner', 'bright');
    log('=====================================', 'bright');
    
    await checkPrerequisites();
    
    const startTime = Date.now();
    let testResults = [];
    
    // Determine which tests to run
    const shouldRunBackend = !options.frontendOnly && !options.e2eOnly;
    const shouldRunFrontend = !options.backendOnly && !options.e2eOnly;
    const shouldRunE2E = !options.unitOnly && !options.backendOnly && !options.frontendOnly;
    
    // Run backend tests
    if (shouldRunBackend || options.backendOnly) {
      try {
        await runBackendTests();
        testResults.push({ suite: 'backend', status: 'passed' });
      } catch (error) {
        testResults.push({ suite: 'backend', status: 'failed', error: error.message });
        if (!options.frontendOnly && !options.e2eOnly) {
          throw error; // Stop execution if not running specific test types
        }
      }
    }
    
    // Run frontend tests
    if (shouldRunFrontend || options.frontendOnly) {
      try {
        await runFrontendTests();
        testResults.push({ suite: 'frontend', status: 'passed' });
      } catch (error) {
        testResults.push({ suite: 'frontend', status: 'failed', error: error.message });
        if (!options.backendOnly && !options.e2eOnly) {
          throw error;
        }
      }
    }
    
    // Run E2E tests
    if (shouldRunE2E || options.e2eOnly) {
      try {
        await runE2ETests();
        testResults.push({ suite: 'e2e', status: 'passed' });
      } catch (error) {
        testResults.push({ suite: 'e2e', status: 'failed', error: error.message });
        if (!options.backendOnly && !options.frontendOnly) {
          throw error;
        }
      }
    }
    
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);
    
    // Print summary
    log('\n📋 Test Summary', 'bright');
    log('===============', 'bright');
    
    const passed = testResults.filter(r => r.status === 'passed').length;
    const failed = testResults.filter(r => r.status === 'failed').length;
    
    testResults.forEach(result => {
      const icon = result.status === 'passed' ? '✅' : '❌';
      const color = result.status === 'passed' ? 'green' : 'red';
      log(`${icon} ${result.suite}: ${result.status.toUpperCase()}`, color);
      
      if (result.error) {
        log(`   Error: ${result.error}`, 'red');
      }
    });
    
    log(`\nTotal: ${testResults.length} | Passed: ${passed} | Failed: ${failed}`, 'cyan');
    log(`Duration: ${duration}s`, 'cyan');
    
    if (options.coverage) {
      await generateTestReport();
    }
    
    if (failed > 0) {
      log('\n❌ Some tests failed!', 'red');
      process.exit(1);
    } else {
      log('\n🎉 All tests passed!', 'green');
      process.exit(0);
    }
    
  } catch (error) {
    log(`\n💥 Test runner failed: ${error.message}`, 'red');
    
    if (options.verbose) {
      console.error(error.stack);
    }
    
    process.exit(1);
  }
}

// Handle process signals
process.on('SIGINT', () => {
  log('\n🛑 Test runner interrupted', 'yellow');
  process.exit(130);
});

process.on('SIGTERM', () => {
  log('\n🛑 Test runner terminated', 'yellow');
  process.exit(143);
});

// Run the main function
main().catch(error => {
  log(`\n💥 Unhandled error: ${error.message}`, 'red');
  process.exit(1);
});
