# Frontend Environment Variables

# API Configuration
VITE_API_BASE_URL=http://localhost:3000

# Blockchain Configuration
VITE_ETH_RPC_URL=https://sepolia.infura.io/v3/YOUR_INFURA_PROJECT_ID
VITE_ETH_WS_URL=wss://sepolia.infura.io/ws/v3/YOUR_INFURA_PROJECT_ID
VITE_BLOCKCHAIN_NETWORK=ethereum
VITE_IS_TESTNET=true

# Smart Contract Addresses (deployed contracts)
VITE_PROFIT_SPLITTER_ADDRESS=0x...
VITE_CAPITAL_RECOVERY_TRACKER_ADDRESS=0x...
VITE_TOKEN_DISTRIBUTOR_ADDRESS=0x...
VITE_PROFIT_TOKEN_ADDRESS=0x...

# KYC Provider Configuration
VITE_KYC_PROVIDER=sumsub
VITE_SUMSUB_APP_TOKEN=your_sumsub_app_token
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_...

# Application Configuration
VITE_APP_NAME=Archimedes Finance
VITE_APP_VERSION=1.0.0
VITE_ENABLE_ANALYTICS=false

# Feature Flags
VITE_ENABLE_PROFIT_SHARING=true
VITE_ENABLE_KYC_VERIFICATION=true
VITE_ENABLE_BLOCKCHAIN_EVENTS=true

# Development Configuration
VITE_DEBUG_MODE=true
VITE_MOCK_DATA=false
