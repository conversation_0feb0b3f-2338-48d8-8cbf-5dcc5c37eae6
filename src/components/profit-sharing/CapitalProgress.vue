<template>
  <div class="capital-progress">
    <div class="capital-progress__header">
      <div class="capital-progress__title">
        <h4>Capital Recovery Progress</h4>
        <span class="capital-progress__stage" :class="stageClass">
          {{ stageDisplay }}
        </span>
      </div>
      <div class="capital-progress__percentage">
        {{ formattedPercentage }}
      </div>
    </div>

    <div class="capital-progress__bar-container">
      <div class="capital-progress__bar">
        <div 
          class="capital-progress__fill" 
          :class="progressClass"
          :style="{ width: `${Math.min(100, percentage)}%` }"
        >
          <div class="capital-progress__shine"></div>
        </div>
      </div>
      
      <div class="capital-progress__markers">
        <div class="capital-progress__marker" style="left: 25%">
          <div class="capital-progress__marker-line"></div>
          <span class="capital-progress__marker-label">25%</span>
        </div>
        <div class="capital-progress__marker" style="left: 50%">
          <div class="capital-progress__marker-line"></div>
          <span class="capital-progress__marker-label">50%</span>
        </div>
        <div class="capital-progress__marker" style="left: 75%">
          <div class="capital-progress__marker-line"></div>
          <span class="capital-progress__marker-label">75%</span>
        </div>
      </div>
    </div>

    <div class="capital-progress__details">
      <div class="capital-progress__detail">
        <span class="capital-progress__detail-label">Target Amount</span>
        <span class="capital-progress__detail-value">{{ formattedTarget }}</span>
      </div>
      <div class="capital-progress__detail">
        <span class="capital-progress__detail-label">Repaid Amount</span>
        <span class="capital-progress__detail-value text-success">{{ formattedRepaid }}</span>
      </div>
      <div class="capital-progress__detail">
        <span class="capital-progress__detail-label">Remaining</span>
        <span class="capital-progress__detail-value text-warning">{{ formattedRemaining }}</span>
      </div>
    </div>

    <div class="capital-progress__timeline" v-if="showTimeline">
      <h5>Recovery Timeline</h5>
      <div class="capital-progress__timeline-items">
        <div 
          v-for="milestone in milestones" 
          :key="milestone.percentage"
          class="capital-progress__timeline-item"
          :class="{ 'capital-progress__timeline-item--completed': percentage >= milestone.percentage }"
        >
          <div class="capital-progress__timeline-icon">
            <i :class="milestone.icon"></i>
          </div>
          <div class="capital-progress__timeline-content">
            <h6>{{ milestone.title }}</h6>
            <p>{{ milestone.description }}</p>
            <small>{{ milestone.amount }}</small>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  targetAmount: {
    type: Number,
    default: 150000
  },
  repaidAmount: {
    type: Number,
    default: 0
  },
  remainingAmount: {
    type: Number,
    default: 150000
  },
  stage: {
    type: String,
    default: 'capital_recovery',
    validator: (value) => ['capital_recovery', 'post_recovery'].includes(value)
  },
  currency: {
    type: String,
    default: 'CAD'
  },
  showTimeline: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const percentage = computed(() => {
  if (props.targetAmount === 0) return 0;
  return Math.min(100, (props.repaidAmount / props.targetAmount) * 100);
});

const formattedPercentage = computed(() => {
  return `${percentage.value.toFixed(1)}%`;
});

const formattedTarget = computed(() => {
  return formatCurrency(props.targetAmount);
});

const formattedRepaid = computed(() => {
  return formatCurrency(props.repaidAmount);
});

const formattedRemaining = computed(() => {
  return formatCurrency(props.remainingAmount);
});

const stageDisplay = computed(() => {
  return props.stage === 'capital_recovery' ? 'Capital Recovery' : 'Post-Recovery';
});

const stageClass = computed(() => {
  return props.stage === 'capital_recovery' ? 'badge bg-warning' : 'badge bg-success';
});

const progressClass = computed(() => {
  if (percentage.value >= 100) return 'capital-progress__fill--complete';
  if (percentage.value >= 75) return 'capital-progress__fill--high';
  if (percentage.value >= 50) return 'capital-progress__fill--medium';
  return 'capital-progress__fill--low';
});

const milestones = computed(() => [
  {
    percentage: 25,
    title: 'First Quarter',
    description: 'Initial capital recovery milestone reached',
    amount: formatCurrency(props.targetAmount * 0.25),
    icon: 'bi bi-flag'
  },
  {
    percentage: 50,
    title: 'Halfway Point',
    description: 'Half of the capital has been recovered',
    amount: formatCurrency(props.targetAmount * 0.5),
    icon: 'bi bi-flag-fill'
  },
  {
    percentage: 75,
    title: 'Three Quarters',
    description: 'Significant progress towards full recovery',
    amount: formatCurrency(props.targetAmount * 0.75),
    icon: 'bi bi-trophy'
  },
  {
    percentage: 100,
    title: 'Full Recovery',
    description: 'Capital recovery complete - entering post-recovery stage',
    amount: formatCurrency(props.targetAmount),
    icon: 'bi bi-trophy-fill'
  }
]);

function formatCurrency(amount) {
  return new Intl.NumberFormat('en-CA', {
    style: 'currency',
    currency: props.currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount || 0);
}
</script>

<style scoped>
.capital-progress {
  background: var(--bs-body-bg);
  border: 1px solid var(--bs-border-color);
  border-radius: 0.75rem;
  padding: 1.5rem;
}

.capital-progress__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
}

.capital-progress__title {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.capital-progress__title h4 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.capital-progress__stage {
  font-size: 0.75rem;
  font-weight: 500;
}

.capital-progress__percentage {
  font-size: 2rem;
  font-weight: 700;
  color: var(--bs-primary);
}

.capital-progress__bar-container {
  position: relative;
  margin-bottom: 2rem;
}

.capital-progress__bar {
  width: 100%;
  height: 1rem;
  background: var(--bs-gray-200);
  border-radius: 0.5rem;
  overflow: hidden;
  position: relative;
}

.capital-progress__fill {
  height: 100%;
  border-radius: 0.5rem;
  position: relative;
  transition: width 0.8s ease-in-out;
  overflow: hidden;
}

.capital-progress__fill--low {
  background: linear-gradient(90deg, #dc3545, #fd7e14);
}

.capital-progress__fill--medium {
  background: linear-gradient(90deg, #fd7e14, #ffc107);
}

.capital-progress__fill--high {
  background: linear-gradient(90deg, #ffc107, #20c997);
}

.capital-progress__fill--complete {
  background: linear-gradient(90deg, #20c997, #198754);
}

.capital-progress__shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shine 2s infinite;
}

@keyframes shine {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.capital-progress__markers {
  position: absolute;
  top: -0.5rem;
  left: 0;
  right: 0;
  height: 2rem;
}

.capital-progress__marker {
  position: absolute;
  transform: translateX(-50%);
}

.capital-progress__marker-line {
  width: 1px;
  height: 0.5rem;
  background: var(--bs-border-color);
  margin: 0 auto 0.25rem;
}

.capital-progress__marker-label {
  font-size: 0.75rem;
  color: var(--bs-secondary);
  white-space: nowrap;
}

.capital-progress__details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.capital-progress__detail {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.capital-progress__detail-label {
  font-size: 0.875rem;
  color: var(--bs-secondary);
  font-weight: 500;
}

.capital-progress__detail-value {
  font-size: 1.125rem;
  font-weight: 600;
}

.capital-progress__timeline {
  border-top: 1px solid var(--bs-border-color);
  padding-top: 1.5rem;
}

.capital-progress__timeline h5 {
  margin-bottom: 1rem;
  font-size: 1rem;
  font-weight: 600;
}

.capital-progress__timeline-items {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.capital-progress__timeline-item {
  display: flex;
  gap: 1rem;
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.capital-progress__timeline-item--completed {
  opacity: 1;
}

.capital-progress__timeline-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background: var(--bs-gray-200);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  color: var(--bs-secondary);
}

.capital-progress__timeline-item--completed .capital-progress__timeline-icon {
  background: var(--bs-success);
  color: white;
}

.capital-progress__timeline-content {
  flex: 1;
}

.capital-progress__timeline-content h6 {
  margin: 0 0 0.25rem 0;
  font-size: 0.875rem;
  font-weight: 600;
}

.capital-progress__timeline-content p {
  margin: 0 0 0.25rem 0;
  font-size: 0.875rem;
  color: var(--bs-secondary);
}

.capital-progress__timeline-content small {
  font-weight: 500;
  color: var(--bs-primary);
}

/* Dark mode adjustments */
[data-bs-theme="dark"] .capital-progress__bar {
  background: var(--bs-gray-800);
}

[data-bs-theme="dark"] .capital-progress__timeline-icon {
  background: var(--bs-gray-700);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .capital-progress {
    padding: 1rem;
  }
  
  .capital-progress__header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
  
  .capital-progress__percentage {
    font-size: 1.5rem;
  }
  
  .capital-progress__details {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
  
  .capital-progress__timeline-item {
    gap: 0.75rem;
  }
  
  .capital-progress__timeline-icon {
    width: 2rem;
    height: 2rem;
  }
}
</style>
