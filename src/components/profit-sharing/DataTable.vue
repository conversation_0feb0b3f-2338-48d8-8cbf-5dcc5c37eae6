<template>
  <div class="data-table">
    <div class="data-table__header" v-if="title || $slots.header">
      <div class="data-table__title-section">
        <h5 v-if="title" class="data-table__title">{{ title }}</h5>
        <p v-if="subtitle" class="data-table__subtitle">{{ subtitle }}</p>
      </div>
      <div class="data-table__actions">
        <slot name="header"></slot>
      </div>
    </div>

    <div class="data-table__filters" v-if="searchable || $slots.filters">
      <div class="data-table__search" v-if="searchable">
        <div class="input-group">
          <span class="input-group-text">
            <i class="bi bi-search"></i>
          </span>
          <input
            type="text"
            class="form-control"
            :placeholder="searchPlaceholder"
            v-model="searchQuery"
          />
        </div>
      </div>
      <div class="data-table__filter-actions">
        <slot name="filters"></slot>
      </div>
    </div>

    <div class="data-table__container">
      <div class="table-responsive">
        <table class="table table-hover">
          <thead>
            <tr>
              <th 
                v-for="column in columns" 
                :key="column.key"
                :class="getColumnClass(column)"
                @click="handleSort(column)"
                :style="{ width: column.width }"
              >
                <div class="data-table__header-content">
                  <span>{{ column.label }}</span>
                  <i 
                    v-if="column.sortable"
                    :class="getSortIcon(column.key)"
                    class="data-table__sort-icon"
                  ></i>
                </div>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr v-if="loading">
              <td :colspan="columns.length" class="text-center py-4">
                <div class="spinner-border spinner-border-sm" role="status">
                  <span class="visually-hidden">Loading...</span>
                </div>
                <span class="ms-2">Loading...</span>
              </td>
            </tr>
            <tr v-else-if="filteredData.length === 0">
              <td :colspan="columns.length" class="text-center py-4 text-muted">
                <i class="bi bi-inbox fs-1 d-block mb-2"></i>
                {{ emptyMessage }}
              </td>
            </tr>
            <tr 
              v-else
              v-for="(item, index) in paginatedData" 
              :key="getRowKey(item, index)"
              @click="handleRowClick(item, index)"
              :class="{ 'data-table__row--clickable': rowClickable }"
            >
              <td 
                v-for="column in columns" 
                :key="column.key"
                :class="getCellClass(column, item)"
              >
                <slot 
                  :name="`cell-${column.key}`" 
                  :item="item" 
                  :value="getCellValue(item, column.key)"
                  :column="column"
                  :index="index"
                >
                  <span v-html="formatCellValue(item, column)"></span>
                </slot>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div class="data-table__footer" v-if="paginated && filteredData.length > 0">
      <div class="data-table__info">
        Showing {{ startIndex + 1 }} to {{ endIndex }} of {{ filteredData.length }} entries
        <span v-if="searchQuery">(filtered from {{ data.length }} total entries)</span>
      </div>
      <nav class="data-table__pagination">
        <ul class="pagination pagination-sm mb-0">
          <li class="page-item" :class="{ disabled: currentPage === 1 }">
            <button class="page-link" @click="goToPage(currentPage - 1)" :disabled="currentPage === 1">
              <i class="bi bi-chevron-left"></i>
            </button>
          </li>
          <li 
            v-for="page in visiblePages" 
            :key="page"
            class="page-item" 
            :class="{ active: page === currentPage }"
          >
            <button class="page-link" @click="goToPage(page)">{{ page }}</button>
          </li>
          <li class="page-item" :class="{ disabled: currentPage === totalPages }">
            <button class="page-link" @click="goToPage(currentPage + 1)" :disabled="currentPage === totalPages">
              <i class="bi bi-chevron-right"></i>
            </button>
          </li>
        </ul>
      </nav>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';

const props = defineProps({
  title: String,
  subtitle: String,
  data: {
    type: Array,
    default: () => []
  },
  columns: {
    type: Array,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  },
  searchable: {
    type: Boolean,
    default: false
  },
  searchPlaceholder: {
    type: String,
    default: 'Search...'
  },
  paginated: {
    type: Boolean,
    default: true
  },
  pageSize: {
    type: Number,
    default: 10
  },
  emptyMessage: {
    type: String,
    default: 'No data available'
  },
  rowClickable: {
    type: Boolean,
    default: false
  },
  rowKey: {
    type: String,
    default: 'id'
  }
});

const emit = defineEmits(['row-click', 'sort-change']);

const searchQuery = ref('');
const currentPage = ref(1);
const sortKey = ref('');
const sortOrder = ref('asc');

const filteredData = computed(() => {
  let filtered = [...props.data];
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(item => {
      return props.columns.some(column => {
        const value = getCellValue(item, column.key);
        return value?.toString().toLowerCase().includes(query);
      });
    });
  }
  
  if (sortKey.value) {
    filtered.sort((a, b) => {
      const aVal = getCellValue(a, sortKey.value);
      const bVal = getCellValue(b, sortKey.value);
      
      let comparison = 0;
      if (aVal < bVal) comparison = -1;
      if (aVal > bVal) comparison = 1;
      
      return sortOrder.value === 'desc' ? -comparison : comparison;
    });
  }
  
  return filtered;
});

const totalPages = computed(() => {
  if (!props.paginated) return 1;
  return Math.ceil(filteredData.value.length / props.pageSize);
});

const startIndex = computed(() => {
  if (!props.paginated) return 0;
  return (currentPage.value - 1) * props.pageSize;
});

const endIndex = computed(() => {
  if (!props.paginated) return filteredData.value.length;
  return Math.min(startIndex.value + props.pageSize, filteredData.value.length);
});

const paginatedData = computed(() => {
  if (!props.paginated) return filteredData.value;
  return filteredData.value.slice(startIndex.value, endIndex.value);
});

const visiblePages = computed(() => {
  const pages = [];
  const maxVisible = 5;
  const half = Math.floor(maxVisible / 2);
  
  let start = Math.max(1, currentPage.value - half);
  let end = Math.min(totalPages.value, start + maxVisible - 1);
  
  if (end - start + 1 < maxVisible) {
    start = Math.max(1, end - maxVisible + 1);
  }
  
  for (let i = start; i <= end; i++) {
    pages.push(i);
  }
  
  return pages;
});

function getCellValue(item, key) {
  return key.split('.').reduce((obj, k) => obj?.[k], item);
}

function formatCellValue(item, column) {
  const value = getCellValue(item, column.key);
  
  if (value === null || value === undefined) {
    return '<span class="text-muted">—</span>';
  }
  
  if (column.format) {
    switch (column.format) {
      case 'currency':
        return new Intl.NumberFormat('en-CA', {
          style: 'currency',
          currency: column.currency || 'CAD'
        }).format(value);
      case 'date':
        return new Date(value).toLocaleDateString();
      case 'datetime':
        return new Date(value).toLocaleString();
      case 'percentage':
        return `${(value * 100).toFixed(2)}%`;
      case 'number':
        return new Intl.NumberFormat().format(value);
      default:
        return value;
    }
  }
  
  return value;
}

function getColumnClass(column) {
  const classes = [];
  if (column.sortable) classes.push('data-table__header--sortable');
  if (column.align) classes.push(`text-${column.align}`);
  return classes.join(' ');
}

function getCellClass(column, item) {
  const classes = [];
  if (column.align) classes.push(`text-${column.align}`);
  if (column.cellClass) {
    if (typeof column.cellClass === 'function') {
      classes.push(column.cellClass(item));
    } else {
      classes.push(column.cellClass);
    }
  }
  return classes.join(' ');
}

function getSortIcon(key) {
  if (sortKey.value !== key) return 'bi bi-arrow-down-up';
  return sortOrder.value === 'asc' ? 'bi bi-arrow-up' : 'bi bi-arrow-down';
}

function handleSort(column) {
  if (!column.sortable) return;
  
  if (sortKey.value === column.key) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
  } else {
    sortKey.value = column.key;
    sortOrder.value = 'asc';
  }
  
  emit('sort-change', { key: sortKey.value, order: sortOrder.value });
}

function handleRowClick(item, index) {
  if (props.rowClickable) {
    emit('row-click', { item, index });
  }
}

function getRowKey(item, index) {
  return getCellValue(item, props.rowKey) || index;
}

function goToPage(page) {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page;
  }
}

// Reset page when search changes
watch(searchQuery, () => {
  currentPage.value = 1;
});

// Reset page when data changes
watch(() => props.data, () => {
  currentPage.value = 1;
});
</script>

<style scoped>
.data-table {
  background: var(--bs-body-bg);
  border: 1px solid var(--bs-border-color);
  border-radius: 0.75rem;
  overflow: hidden;
}

.data-table__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1.5rem 1.5rem 0;
  gap: 1rem;
}

.data-table__title-section {
  flex: 1;
}

.data-table__title {
  margin: 0 0 0.25rem 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.data-table__subtitle {
  margin: 0;
  color: var(--bs-secondary);
  font-size: 0.875rem;
}

.data-table__filters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  gap: 1rem;
  border-bottom: 1px solid var(--bs-border-color);
}

.data-table__search {
  flex: 1;
  max-width: 300px;
}

.data-table__container {
  min-height: 200px;
}

.data-table__header-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.data-table__header--sortable {
  cursor: pointer;
  user-select: none;
}

.data-table__header--sortable:hover {
  background-color: var(--bs-gray-100);
}

.data-table__sort-icon {
  opacity: 0.5;
  transition: opacity 0.2s ease;
}

.data-table__header--sortable:hover .data-table__sort-icon {
  opacity: 1;
}

.data-table__row--clickable {
  cursor: pointer;
}

.data-table__row--clickable:hover {
  background-color: var(--bs-gray-50);
}

.data-table__footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--bs-border-color);
  background: var(--bs-gray-50);
}

.data-table__info {
  font-size: 0.875rem;
  color: var(--bs-secondary);
}

.table {
  margin: 0;
}

.table th {
  border-top: none;
  font-weight: 600;
  font-size: 0.875rem;
  color: var(--bs-secondary);
  background: var(--bs-gray-50);
}

.table td {
  vertical-align: middle;
}

/* Dark mode adjustments */
[data-bs-theme="dark"] .data-table__header--sortable:hover {
  background-color: var(--bs-gray-800);
}

[data-bs-theme="dark"] .data-table__row--clickable:hover {
  background-color: var(--bs-gray-800);
}

[data-bs-theme="dark"] .data-table__footer {
  background: var(--bs-gray-800);
}

[data-bs-theme="dark"] .table th {
  background: var(--bs-gray-800);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .data-table__header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .data-table__filters {
    flex-direction: column;
    align-items: stretch;
  }
  
  .data-table__search {
    max-width: none;
  }
  
  .data-table__footer {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .data-table__pagination {
    display: flex;
    justify-content: center;
  }
}
</style>
