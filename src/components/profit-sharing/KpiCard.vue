<template>
  <div class="kpi-card" :class="{ 'kpi-card--loading': loading }">
    <div class="kpi-card__header">
      <div class="kpi-card__icon" v-if="icon">
        <i :class="icon"></i>
      </div>
      <div class="kpi-card__title-section">
        <h3 class="kpi-card__title">{{ title }}</h3>
        <p class="kpi-card__subtitle" v-if="subtitle">{{ subtitle }}</p>
      </div>
    </div>
    
    <div class="kpi-card__content">
      <div class="kpi-card__value" :class="valueClass">
        <span v-if="loading" class="kpi-card__skeleton"></span>
        <span v-else>{{ formattedValue }}</span>
      </div>
      
      <div class="kpi-card__change" v-if="change !== null && !loading">
        <i :class="changeIcon"></i>
        <span :class="changeClass">{{ formattedChange }}</span>
      </div>
      
      <div class="kpi-card__description" v-if="description && !loading">
        {{ description }}
      </div>
    </div>
    
    <div class="kpi-card__footer" v-if="$slots.footer">
      <slot name="footer"></slot>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  subtitle: {
    type: String,
    default: null
  },
  value: {
    type: [String, Number],
    required: true
  },
  change: {
    type: Number,
    default: null
  },
  description: {
    type: String,
    default: null
  },
  icon: {
    type: String,
    default: null
  },
  format: {
    type: String,
    default: 'text', // 'currency', 'percentage', 'number', 'text'
    validator: (value) => ['currency', 'percentage', 'number', 'text'].includes(value)
  },
  currency: {
    type: String,
    default: 'CAD'
  },
  loading: {
    type: Boolean,
    default: false
  },
  variant: {
    type: String,
    default: 'default', // 'default', 'success', 'warning', 'danger', 'info'
    validator: (value) => ['default', 'success', 'warning', 'danger', 'info'].includes(value)
  }
});

const formattedValue = computed(() => {
  if (props.loading) return '';
  
  switch (props.format) {
    case 'currency':
      return new Intl.NumberFormat('en-CA', {
        style: 'currency',
        currency: props.currency,
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
      }).format(props.value || 0);
      
    case 'percentage':
      return `${(props.value || 0).toFixed(2)}%`;
      
    case 'number':
      return new Intl.NumberFormat('en-CA').format(props.value || 0);
      
    default:
      return props.value?.toString() || '';
  }
});

const formattedChange = computed(() => {
  if (props.change === null) return '';
  
  const absChange = Math.abs(props.change);
  const sign = props.change >= 0 ? '+' : '-';
  
  return `${sign}${absChange.toFixed(2)}%`;
});

const changeIcon = computed(() => {
  if (props.change === null) return '';
  return props.change >= 0 ? 'bi bi-arrow-up' : 'bi bi-arrow-down';
});

const changeClass = computed(() => {
  if (props.change === null) return '';
  return props.change >= 0 ? 'text-success' : 'text-danger';
});

const valueClass = computed(() => {
  return `kpi-card__value--${props.variant}`;
});
</script>

<style scoped>
.kpi-card {
  background: var(--bs-body-bg);
  border: 1px solid var(--bs-border-color);
  border-radius: 0.75rem;
  padding: 1.5rem;
  transition: all 0.2s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.kpi-card:hover {
  border-color: var(--bs-primary);
  box-shadow: 0 4px 12px rgba(var(--bs-primary-rgb), 0.15);
}

.kpi-card--loading {
  opacity: 0.7;
}

.kpi-card__header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1rem;
}

.kpi-card__icon {
  width: 3rem;
  height: 3rem;
  border-radius: 0.5rem;
  background: rgba(var(--bs-primary-rgb), 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--bs-primary);
  font-size: 1.25rem;
  flex-shrink: 0;
}

.kpi-card__title-section {
  flex: 1;
  min-width: 0;
}

.kpi-card__title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--bs-secondary);
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.kpi-card__subtitle {
  font-size: 0.75rem;
  color: var(--bs-secondary);
  margin: 0.25rem 0 0 0;
  opacity: 0.8;
}

.kpi-card__content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.kpi-card__value {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1.2;
  color: var(--bs-body-color);
}

.kpi-card__value--success {
  color: var(--bs-success);
}

.kpi-card__value--warning {
  color: var(--bs-warning);
}

.kpi-card__value--danger {
  color: var(--bs-danger);
}

.kpi-card__value--info {
  color: var(--bs-info);
}

.kpi-card__skeleton {
  display: inline-block;
  width: 8rem;
  height: 2rem;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 0.25rem;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.kpi-card__change {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.kpi-card__description {
  font-size: 0.875rem;
  color: var(--bs-secondary);
  margin-top: auto;
  padding-top: 0.5rem;
}

.kpi-card__footer {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--bs-border-color);
}

/* Dark mode adjustments */
[data-bs-theme="dark"] .kpi-card__skeleton {
  background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
  background-size: 200% 100%;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .kpi-card {
    padding: 1rem;
  }
  
  .kpi-card__header {
    gap: 0.75rem;
  }
  
  .kpi-card__icon {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1rem;
  }
  
  .kpi-card__value {
    font-size: 1.5rem;
  }
}
</style>
