<template>
  <span class="tx-hash-link" v-if="hash">
    <a 
      :href="explorerUrl" 
      target="_blank" 
      rel="noopener noreferrer"
      class="tx-hash-link__link"
      :title="fullHash"
      @click="trackClick"
    >
      <i class="bi bi-box-arrow-up-right me-1"></i>
      {{ displayHash }}
    </a>
    <button 
      v-if="copyable"
      class="btn btn-link btn-sm p-0 ms-2 tx-hash-link__copy"
      @click="copyToClipboard"
      :title="copied ? 'Copied!' : 'Copy transaction hash'"
    >
      <i :class="copyIcon"></i>
    </button>
  </span>
  <span v-else class="text-muted">—</span>
</template>

<script setup>
import { computed, ref } from 'vue';
import { useToast } from 'vue-toastification';

const props = defineProps({
  hash: {
    type: String,
    default: null
  },
  network: {
    type: String,
    default: 'ethereum', // 'ethereum', 'polygon', 'bsc', etc.
    validator: (value) => ['ethereum', 'polygon', 'bsc', 'arbitrum', 'optimism'].includes(value)
  },
  testnet: {
    type: Boolean,
    default: false
  },
  truncate: {
    type: Boolean,
    default: true
  },
  truncateLength: {
    type: Number,
    default: 8
  },
  copyable: {
    type: Boolean,
    default: true
  },
  trackClicks: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['click', 'copy']);

const toast = useToast();
const copied = ref(false);

const explorerUrl = computed(() => {
  if (!props.hash) return '';
  
  const baseUrls = {
    ethereum: props.testnet ? 'https://sepolia.etherscan.io' : 'https://etherscan.io',
    polygon: props.testnet ? 'https://mumbai.polygonscan.com' : 'https://polygonscan.com',
    bsc: props.testnet ? 'https://testnet.bscscan.com' : 'https://bscscan.com',
    arbitrum: props.testnet ? 'https://testnet.arbiscan.io' : 'https://arbiscan.io',
    optimism: props.testnet ? 'https://kovan-optimistic.etherscan.io' : 'https://optimistic.etherscan.io'
  };
  
  const baseUrl = baseUrls[props.network] || baseUrls.ethereum;
  return `${baseUrl}/tx/${props.hash}`;
});

const fullHash = computed(() => {
  return props.hash || '';
});

const displayHash = computed(() => {
  if (!props.hash) return '';
  
  if (!props.truncate) return props.hash;
  
  const length = props.truncateLength;
  if (props.hash.length <= length * 2 + 3) return props.hash;
  
  return `${props.hash.slice(0, length)}...${props.hash.slice(-length)}`;
});

const copyIcon = computed(() => {
  return copied.value ? 'bi bi-check text-success' : 'bi bi-copy';
});

async function copyToClipboard() {
  if (!props.hash) return;
  
  try {
    await navigator.clipboard.writeText(props.hash);
    copied.value = true;
    
    setTimeout(() => {
      copied.value = false;
    }, 2000);
    
    toast.success('Transaction hash copied to clipboard', {
      timeout: 2000
    });
    
    emit('copy', props.hash);
  } catch (error) {
    console.error('Failed to copy to clipboard:', error);
    toast.error('Failed to copy transaction hash');
  }
}

function trackClick() {
  if (props.trackClicks) {
    emit('click', {
      hash: props.hash,
      network: props.network,
      explorerUrl: explorerUrl.value
    });
  }
}
</script>

<style scoped>
.tx-hash-link {
  display: inline-flex;
  align-items: center;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
}

.tx-hash-link__link {
  color: var(--bs-primary);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  background: rgba(var(--bs-primary-rgb), 0.1);
  border: 1px solid rgba(var(--bs-primary-rgb), 0.2);
  transition: all 0.2s ease;
  font-weight: 500;
}

.tx-hash-link__link:hover {
  color: var(--bs-primary);
  background: rgba(var(--bs-primary-rgb), 0.15);
  border-color: rgba(var(--bs-primary-rgb), 0.3);
  text-decoration: none;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(var(--bs-primary-rgb), 0.2);
}

.tx-hash-link__link:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(var(--bs-primary-rgb), 0.2);
}

.tx-hash-link__copy {
  color: var(--bs-secondary);
  border: none;
  background: none;
  font-size: 0.875rem;
  line-height: 1;
  transition: color 0.2s ease;
}

.tx-hash-link__copy:hover {
  color: var(--bs-primary);
}

.tx-hash-link__copy:focus {
  box-shadow: none;
  outline: 2px solid rgba(var(--bs-primary-rgb), 0.25);
  outline-offset: 2px;
}

/* Network-specific styling */
.tx-hash-link--ethereum .tx-hash-link__link {
  background: rgba(98, 126, 234, 0.1);
  border-color: rgba(98, 126, 234, 0.2);
  color: #627eea;
}

.tx-hash-link--ethereum .tx-hash-link__link:hover {
  background: rgba(98, 126, 234, 0.15);
  border-color: rgba(98, 126, 234, 0.3);
  color: #627eea;
}

.tx-hash-link--polygon .tx-hash-link__link {
  background: rgba(130, 71, 229, 0.1);
  border-color: rgba(130, 71, 229, 0.2);
  color: #8247e5;
}

.tx-hash-link--polygon .tx-hash-link__link:hover {
  background: rgba(130, 71, 229, 0.15);
  border-color: rgba(130, 71, 229, 0.3);
  color: #8247e5;
}

.tx-hash-link--bsc .tx-hash-link__link {
  background: rgba(243, 186, 47, 0.1);
  border-color: rgba(243, 186, 47, 0.2);
  color: #f3ba2f;
}

.tx-hash-link--bsc .tx-hash-link__link:hover {
  background: rgba(243, 186, 47, 0.15);
  border-color: rgba(243, 186, 47, 0.3);
  color: #f3ba2f;
}

/* Dark mode adjustments */
[data-bs-theme="dark"] .tx-hash-link__link {
  background: rgba(var(--bs-primary-rgb), 0.2);
  border-color: rgba(var(--bs-primary-rgb), 0.3);
}

[data-bs-theme="dark"] .tx-hash-link__link:hover {
  background: rgba(var(--bs-primary-rgb), 0.25);
  border-color: rgba(var(--bs-primary-rgb), 0.4);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .tx-hash-link {
    font-size: 0.75rem;
  }
  
  .tx-hash-link__link {
    padding: 0.125rem 0.375rem;
  }
}

/* Loading state */
.tx-hash-link--loading .tx-hash-link__link {
  background: var(--bs-gray-200);
  border-color: var(--bs-gray-300);
  color: var(--bs-gray-500);
  cursor: not-allowed;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Accessibility improvements */
.tx-hash-link__link:focus {
  outline: 2px solid rgba(var(--bs-primary-rgb), 0.5);
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .tx-hash-link__link {
    color: black !important;
    background: none !important;
    border: 1px solid black !important;
  }
  
  .tx-hash-link__copy {
    display: none;
  }
  
  .tx-hash-link__link::after {
    content: " (" attr(href) ")";
    font-size: 0.75em;
    color: #666;
  }
}
</style>
