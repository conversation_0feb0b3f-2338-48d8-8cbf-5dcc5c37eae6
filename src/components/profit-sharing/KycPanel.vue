<template>
  <div class="kyc-panel" :class="panelClass">
    <div class="kyc-panel__header">
      <div class="kyc-panel__status">
        <div class="kyc-panel__status-icon">
          <i :class="statusIcon"></i>
        </div>
        <div class="kyc-panel__status-text">
          <h5>KYC/AML Verification</h5>
          <span class="kyc-panel__status-badge" :class="statusBadgeClass">
            {{ statusText }}
          </span>
        </div>
      </div>
      <div class="kyc-panel__actions" v-if="!verified && !loading">
        <button 
          class="btn btn-primary btn-sm"
          @click="initiateVerification"
          :disabled="initiating"
        >
          <span v-if="initiating" class="spinner-border spinner-border-sm me-2"></span>
          {{ initiating ? 'Starting...' : 'Start Verification' }}
        </button>
      </div>
    </div>

    <div class="kyc-panel__content">
      <div v-if="loading" class="kyc-panel__loading">
        <div class="spinner-border" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <p>Loading verification status...</p>
      </div>

      <div v-else-if="verified" class="kyc-panel__verified">
        <div class="kyc-panel__verified-content">
          <i class="bi bi-check-circle-fill text-success fs-1"></i>
          <h6>Verification Complete</h6>
          <p class="text-muted">Your identity has been verified and you are eligible to receive distributions.</p>
          <div class="kyc-panel__verified-details" v-if="verificationDate">
            <small class="text-muted">
              Verified on {{ formatDate(verificationDate) }}
            </small>
          </div>
        </div>
      </div>

      <div v-else-if="status === 'pending'" class="kyc-panel__pending">
        <div class="kyc-panel__pending-content">
          <i class="bi bi-clock text-warning fs-1"></i>
          <h6>Verification Pending</h6>
          <p class="text-muted">Your verification is being reviewed. This typically takes 1-3 business days.</p>
          <div class="kyc-panel__pending-actions">
            <button class="btn btn-outline-primary btn-sm" @click="checkStatus">
              <i class="bi bi-arrow-clockwise me-1"></i>
              Check Status
            </button>
          </div>
        </div>
      </div>

      <div v-else-if="status === 'rejected'" class="kyc-panel__rejected">
        <div class="kyc-panel__rejected-content">
          <i class="bi bi-x-circle-fill text-danger fs-1"></i>
          <h6>Verification Failed</h6>
          <p class="text-muted">{{ rejectionReason || 'Your verification was not successful. Please try again with different documents.' }}</p>
          <div class="kyc-panel__rejected-actions">
            <button class="btn btn-primary btn-sm" @click="initiateVerification">
              <i class="bi bi-arrow-repeat me-1"></i>
              Try Again
            </button>
          </div>
        </div>
      </div>

      <div v-else class="kyc-panel__unverified">
        <div class="kyc-panel__unverified-content">
          <i class="bi bi-shield-exclamation text-warning fs-1"></i>
          <h6>Verification Required</h6>
          <p class="text-muted">Complete KYC/AML verification to be eligible for profit distributions.</p>
          
          <div class="kyc-panel__requirements">
            <h6>Required Documents:</h6>
            <ul class="kyc-panel__requirements-list">
              <li>
                <i class="bi bi-check-circle text-success me-2"></i>
                Government-issued photo ID (passport, driver's license)
              </li>
              <li>
                <i class="bi bi-check-circle text-success me-2"></i>
                Proof of address (utility bill, bank statement)
              </li>
              <li>
                <i class="bi bi-check-circle text-success me-2"></i>
                Selfie for identity verification
              </li>
            </ul>
          </div>

          <div class="kyc-panel__benefits">
            <h6>Benefits of Verification:</h6>
            <ul class="kyc-panel__benefits-list">
              <li>
                <i class="bi bi-currency-dollar text-primary me-2"></i>
                Eligible for profit distributions
              </li>
              <li>
                <i class="bi bi-shield-check text-primary me-2"></i>
                Enhanced account security
              </li>
              <li>
                <i class="bi bi-graph-up text-primary me-2"></i>
                Access to detailed analytics
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <div class="kyc-panel__footer" v-if="showFooter">
      <div class="kyc-panel__footer-content">
        <small class="text-muted">
          <i class="bi bi-shield-lock me-1"></i>
          Your personal information is encrypted and securely stored in compliance with privacy regulations.
        </small>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue';
import { useToast } from 'vue-toastification';
import { useProfitSharingStore } from '@/stores/profitSharing';

const props = defineProps({
  verified: {
    type: Boolean,
    default: false
  },
  status: {
    type: String,
    default: 'unverified', // 'unverified', 'pending', 'verified', 'rejected'
    validator: (value) => ['unverified', 'pending', 'verified', 'rejected'].includes(value)
  },
  verificationDate: {
    type: [String, Date],
    default: null
  },
  rejectionReason: {
    type: String,
    default: null
  },
  investorId: {
    type: [String, Number],
    required: true
  },
  tokenId: {
    type: [String, Number],
    default: 1
  },
  loading: {
    type: Boolean,
    default: false
  },
  showFooter: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['verification-initiated', 'status-checked']);

const toast = useToast();
const profitSharingStore = useProfitSharingStore();
const initiating = ref(false);

const panelClass = computed(() => {
  return `kyc-panel--${props.status}`;
});

const statusIcon = computed(() => {
  switch (props.status) {
    case 'verified':
      return 'bi bi-shield-check-fill text-success';
    case 'pending':
      return 'bi bi-clock text-warning';
    case 'rejected':
      return 'bi bi-shield-x text-danger';
    default:
      return 'bi bi-shield-exclamation text-warning';
  }
});

const statusText = computed(() => {
  switch (props.status) {
    case 'verified':
      return 'Verified';
    case 'pending':
      return 'Pending Review';
    case 'rejected':
      return 'Verification Failed';
    default:
      return 'Not Verified';
  }
});

const statusBadgeClass = computed(() => {
  switch (props.status) {
    case 'verified':
      return 'badge bg-success';
    case 'pending':
      return 'badge bg-warning';
    case 'rejected':
      return 'badge bg-danger';
    default:
      return 'badge bg-secondary';
  }
});

async function initiateVerification() {
  try {
    initiating.value = true;
    
    const result = await profitSharingStore.initiateKYC(props.investorId, props.tokenId);
    
    if (result.redirectUrl) {
      // Open KYC provider in new window
      window.open(result.redirectUrl, 'kyc-verification', 'width=800,height=600');
    } else if (result.clientToken) {
      // Initialize KYC widget (implementation depends on provider)
      initializeKYCWidget(result.clientToken);
    }
    
    emit('verification-initiated', result);
    toast.success('KYC verification process started');
    
  } catch (error) {
    console.error('Failed to initiate KYC:', error);
    toast.error('Failed to start verification process');
  } finally {
    initiating.value = false;
  }
}

async function checkStatus() {
  try {
    // Refresh investor dashboard to get latest KYC status
    await profitSharingStore.fetchInvestorDashboard(props.investorId);
    emit('status-checked');
    toast.info('Status updated');
  } catch (error) {
    console.error('Failed to check KYC status:', error);
    toast.error('Failed to check verification status');
  }
}

function initializeKYCWidget(clientToken) {
  // This would integrate with your chosen KYC provider's widget
  // Example for Sumsub:
  /*
  const snsWebSdkInstance = snsWebSdk.init(clientToken, () => {
    // Verification completed
    checkStatus();
  });
  snsWebSdkInstance.launch('#kyc-container');
  */
  
  console.log('Initialize KYC widget with token:', clientToken);
}

function formatDate(date) {
  if (!date) return '';
  return new Date(date).toLocaleDateString('en-CA', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}
</script>

<style scoped>
.kyc-panel {
  background: var(--bs-body-bg);
  border: 1px solid var(--bs-border-color);
  border-radius: 0.75rem;
  overflow: hidden;
}

.kyc-panel--verified {
  border-color: var(--bs-success);
}

.kyc-panel--pending {
  border-color: var(--bs-warning);
}

.kyc-panel--rejected {
  border-color: var(--bs-danger);
}

.kyc-panel__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1.5rem;
  border-bottom: 1px solid var(--bs-border-color);
  gap: 1rem;
}

.kyc-panel__status {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  flex: 1;
}

.kyc-panel__status-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.kyc-panel__status-text h5 {
  margin: 0 0 0.5rem 0;
  font-size: 1.125rem;
  font-weight: 600;
}

.kyc-panel__status-badge {
  font-size: 0.75rem;
}

.kyc-panel__content {
  padding: 1.5rem;
}

.kyc-panel__loading,
.kyc-panel__verified,
.kyc-panel__pending,
.kyc-panel__rejected,
.kyc-panel__unverified {
  text-align: center;
}

.kyc-panel__verified-content,
.kyc-panel__pending-content,
.kyc-panel__rejected-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.kyc-panel__verified-content h6,
.kyc-panel__pending-content h6,
.kyc-panel__rejected-content h6 {
  margin: 0;
  font-weight: 600;
}

.kyc-panel__unverified-content {
  text-align: left;
}

.kyc-panel__unverified-content > i {
  display: block;
  text-align: center;
  margin-bottom: 1rem;
}

.kyc-panel__unverified-content h6 {
  text-align: center;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.kyc-panel__unverified-content > p {
  text-align: center;
  margin-bottom: 1.5rem;
}

.kyc-panel__requirements,
.kyc-panel__benefits {
  margin-bottom: 1.5rem;
}

.kyc-panel__requirements h6,
.kyc-panel__benefits h6 {
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: var(--bs-body-color);
}

.kyc-panel__requirements-list,
.kyc-panel__benefits-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.kyc-panel__requirements-list li,
.kyc-panel__benefits-list li {
  display: flex;
  align-items: center;
  padding: 0.5rem 0;
  font-size: 0.875rem;
}

.kyc-panel__footer {
  padding: 1rem 1.5rem;
  background: var(--bs-gray-50);
  border-top: 1px solid var(--bs-border-color);
}

.kyc-panel__footer-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Dark mode adjustments */
[data-bs-theme="dark"] .kyc-panel__footer {
  background: var(--bs-gray-800);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .kyc-panel__header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .kyc-panel__status {
    align-items: center;
  }
  
  .kyc-panel__content {
    padding: 1rem;
  }
  
  .kyc-panel__footer {
    padding: 1rem;
  }
}
</style>
