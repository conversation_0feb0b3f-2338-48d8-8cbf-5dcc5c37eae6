import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { profitSharingApi, kycApi } from '@/api/profitSharing';
import { useToast } from 'vue-toastification';

export const useProfitSharingStore = defineStore('profitSharing', () => {
  const toast = useToast();

  // State
  const capitalStatus = ref({
    target_amount: 150000,
    repaid_amount: 0,
    stage: 'capital_recovery',
    remaining_amount: 150000,
    recovery_percentage: 0,
    last_updated: null
  });

  const investorDashboard = ref({
    capital_status: null,
    token_balances: [],
    recent_payouts: [],
    total_received: 0
  });

  const adminDashboard = ref({
    capital_status: null,
    recent_distributions: [],
    recent_profit_submissions: [],
    statistics: {
      verified_investors: 0,
      pending_kyc: 0,
      total_distributed: 0,
      total_profits: 0
    }
  });

  const recentEvents = ref([]);
  const eventStats = ref([]);
  const serviceHealth = ref({
    initialized: false,
    listening: false,
    last_processed_block: 0
  });

  const loading = ref({
    capitalStatus: false,
    investorDashboard: false,
    adminDashboard: false,
    profitSubmission: false,
    kycInitiation: false
  });

  const errors = ref({});

  // Computed
  const isCapitalRecoveryStage = computed(() => {
    return capitalStatus.value.stage === 'capital_recovery';
  });

  const capitalRecoveryProgress = computed(() => {
    return Math.min(100, capitalStatus.value.recovery_percentage || 0);
  });

  const formattedCapitalStatus = computed(() => {
    return {
      ...capitalStatus.value,
      target_amount_formatted: formatCurrency(capitalStatus.value.target_amount),
      repaid_amount_formatted: formatCurrency(capitalStatus.value.repaid_amount),
      remaining_amount_formatted: formatCurrency(capitalStatus.value.remaining_amount),
      stage_display: capitalStatus.value.stage === 'capital_recovery' ? 'Capital Recovery' : 'Post-Recovery'
    };
  });

  const totalTokenBalance = computed(() => {
    return investorDashboard.value.token_balances.reduce((total, balance) => {
      return total + parseFloat(balance.balance || 0);
    }, 0);
  });

  // Actions
  async function fetchCapitalStatus() {
    loading.value.capitalStatus = true;
    errors.value.capitalStatus = null;

    try {
      const response = await profitSharingApi.getCapitalStatus();
      if (response.success) {
        capitalStatus.value = response.data;
      }
    } catch (error) {
      errors.value.capitalStatus = error.message;
      toast.error('Failed to fetch capital status');
    } finally {
      loading.value.capitalStatus = false;
    }
  }

  async function fetchInvestorDashboard(investorId) {
    loading.value.investorDashboard = true;
    errors.value.investorDashboard = null;

    try {
      const response = await profitSharingApi.getInvestorDashboard(investorId);
      if (response.success) {
        investorDashboard.value = response.data;
        if (response.data.capital_status) {
          capitalStatus.value = response.data.capital_status;
        }
      }
    } catch (error) {
      errors.value.investorDashboard = error.message;
      toast.error('Failed to fetch investor dashboard');
    } finally {
      loading.value.investorDashboard = false;
    }
  }

  async function fetchAdminDashboard() {
    loading.value.adminDashboard = true;
    errors.value.adminDashboard = null;

    try {
      const response = await profitSharingApi.getAdminDashboard();
      if (response.success) {
        adminDashboard.value = response.data;
        if (response.data.capital_status) {
          capitalStatus.value = response.data.capital_status;
        }
      }
    } catch (error) {
      errors.value.adminDashboard = error.message;
      toast.error('Failed to fetch admin dashboard');
    } finally {
      loading.value.adminDashboard = false;
    }
  }

  async function submitMonthlyProfit(profitData) {
    loading.value.profitSubmission = true;
    errors.value.profitSubmission = null;

    try {
      const response = await profitSharingApi.submitProfit(profitData);
      if (response.success) {
        toast.success('Monthly profit submitted successfully');
        
        // Refresh dashboard data
        await fetchAdminDashboard();
        await fetchCapitalStatus();
        
        return response.data;
      }
    } catch (error) {
      errors.value.profitSubmission = error.message;
      toast.error('Failed to submit monthly profit');
      throw error;
    } finally {
      loading.value.profitSubmission = false;
    }
  }

  async function initiateKYC(investorId, tokenId) {
    loading.value.kycInitiation = true;
    errors.value.kycInitiation = null;

    try {
      const response = await kycApi.initiateVerification(investorId);
      if (response.success) {
        toast.success('KYC verification initiated');
        return response.data;
      }
    } catch (error) {
      errors.value.kycInitiation = error.message;
      toast.error('Failed to initiate KYC verification');
      throw error;
    } finally {
      loading.value.kycInitiation = false;
    }
  }

  async function updateKYCStatus(investorId, tokenId, verified, metadata = {}) {
    try {
      const response = await profitSharingApi.updateKYCStatus(investorId, tokenId, verified, metadata);
      if (response.success) {
        toast.success(`KYC status updated: ${verified ? 'Verified' : 'Unverified'}`);
        
        // Refresh relevant data
        await fetchInvestorDashboard(investorId);
        
        return response.data;
      }
    } catch (error) {
      toast.error('Failed to update KYC status');
      throw error;
    }
  }

  async function fetchRecentEvents(limit = 50, eventType = null) {
    try {
      const response = await profitSharingApi.getRecentEvents(limit, eventType);
      if (response.success) {
        recentEvents.value = response.data;
      }
    } catch (error) {
      console.error('Failed to fetch recent events:', error);
    }
  }

  async function fetchEventStats() {
    try {
      const response = await profitSharingApi.getEventStats();
      if (response.success) {
        eventStats.value = response.data;
      }
    } catch (error) {
      console.error('Failed to fetch event stats:', error);
    }
  }

  async function fetchServiceHealth() {
    try {
      const response = await profitSharingApi.getServiceHealth();
      if (response.success) {
        serviceHealth.value = response.data;
      }
    } catch (error) {
      console.error('Failed to fetch service health:', error);
    }
  }

  async function restartEventService() {
    try {
      const response = await profitSharingApi.restartService();
      if (response.success) {
        toast.success('Event service restarted successfully');
        await fetchServiceHealth();
      }
    } catch (error) {
      toast.error('Failed to restart event service');
      throw error;
    }
  }

  // Refresh functions for event listener
  async function refreshCapitalStatus() {
    await fetchCapitalStatus();
  }

  async function refreshDashboardData() {
    // Refresh both investor and admin dashboards if they have data
    if (investorDashboard.value.capital_status) {
      const currentUser = getCurrentUser(); // You'll need to implement this
      if (currentUser?.id) {
        await fetchInvestorDashboard(currentUser.id);
      }
    }
    
    if (adminDashboard.value.capital_status) {
      await fetchAdminDashboard();
    }
  }

  async function refreshInvestorData() {
    const currentUser = getCurrentUser(); // You'll need to implement this
    if (currentUser?.id) {
      await fetchInvestorDashboard(currentUser.id);
    }
  }

  // Utility functions
  function formatCurrency(amount, currency = 'CAD') {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount || 0);
  }

  function formatTokenAmount(amount, decimals = 18) {
    const value = parseFloat(amount || 0);
    if (value === 0) return '0';
    
    if (value < 0.001) {
      return value.toExponential(2);
    }
    
    return value.toLocaleString('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 6
    });
  }

  function formatPercentage(value) {
    return `${(value || 0).toFixed(2)}%`;
  }

  function getCurrentUser() {
    // This should integrate with your existing auth store
    // For now, return a placeholder
    const authStore = useAuthStore?.();
    return authStore?.user || null;
  }

  // Reset functions
  function resetState() {
    capitalStatus.value = {
      target_amount: 150000,
      repaid_amount: 0,
      stage: 'capital_recovery',
      remaining_amount: 150000,
      recovery_percentage: 0,
      last_updated: null
    };
    
    investorDashboard.value = {
      capital_status: null,
      token_balances: [],
      recent_payouts: [],
      total_received: 0
    };
    
    adminDashboard.value = {
      capital_status: null,
      recent_distributions: [],
      recent_profit_submissions: [],
      statistics: {
        verified_investors: 0,
        pending_kyc: 0,
        total_distributed: 0,
        total_profits: 0
      }
    };
    
    recentEvents.value = [];
    eventStats.value = [];
    errors.value = {};
  }

  return {
    // State
    capitalStatus,
    investorDashboard,
    adminDashboard,
    recentEvents,
    eventStats,
    serviceHealth,
    loading,
    errors,

    // Computed
    isCapitalRecoveryStage,
    capitalRecoveryProgress,
    formattedCapitalStatus,
    totalTokenBalance,

    // Actions
    fetchCapitalStatus,
    fetchInvestorDashboard,
    fetchAdminDashboard,
    submitMonthlyProfit,
    initiateKYC,
    updateKYCStatus,
    fetchRecentEvents,
    fetchEventStats,
    fetchServiceHealth,
    restartEventService,

    // Refresh functions
    refreshCapitalStatus,
    refreshDashboardData,
    refreshInvestorData,

    // Utility functions
    formatCurrency,
    formatTokenAmount,
    formatPercentage,

    // Reset
    resetState
  };
});
