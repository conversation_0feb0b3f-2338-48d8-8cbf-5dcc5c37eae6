import { createRouter, createWebHistory } from 'vue-router';
import { useToast } from 'vue-toastification';

// Import views
const Home = () => import('@/views/Home.vue');
const Login = () => import('@/views/Login.vue');
const Register = () => import('@/views/Register.vue');
const Dashboard = () => import('@/views/Dashboard.vue');
const InvestorDashboard = () => import('@/views/InvestorDashboard.vue');
const AdminDashboard = () => import('@/views/AdminDashboard.vue');
const Profile = () => import('@/views/Profile.vue');
const Settings = () => import('@/views/Settings.vue');
const HelpSupport = () => import('@/views/HelpSupport.vue');
const Messages = () => import('@/views/Messages.vue');

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: {
      title: 'Archimedes Finance',
      requiresAuth: false
    }
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: {
      title: 'Login - Archimedes Finance',
      requiresAuth: false,
      hideForAuth: true
    }
  },
  {
    path: '/register',
    name: 'Register',
    component: Register,
    meta: {
      title: 'Register - Archimedes Finance',
      requiresAuth: false,
      hideForAuth: true
    }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: {
      title: 'Dashboard - Archimedes Finance',
      requiresAuth: true
    }
  },
  {
    path: '/dashboard/investor',
    name: 'InvestorDashboard',
    component: InvestorDashboard,
    meta: {
      title: 'Investor Dashboard - Archimedes Finance',
      requiresAuth: true,
      roles: ['investor', 'client']
    }
  },
  {
    path: '/dashboard/admin',
    name: 'AdminDashboard',
    component: AdminDashboard,
    meta: {
      title: 'Admin Dashboard - Archimedes Finance',
      requiresAuth: true,
      roles: ['manager', 'admin']
    }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: Profile,
    meta: {
      title: 'Profile - Archimedes Finance',
      requiresAuth: true
    }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: Settings,
    meta: {
      title: 'Settings - Archimedes Finance',
      requiresAuth: true
    }
  },
  {
    path: '/help',
    name: 'HelpSupport',
    component: HelpSupport,
    meta: {
      title: 'Help & Support - Archimedes Finance',
      requiresAuth: false
    }
  },
  {
    path: '/messages',
    name: 'Messages',
    component: Messages,
    meta: {
      title: 'Messages - Archimedes Finance',
      requiresAuth: true
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: {
      title: 'Page Not Found - Archimedes Finance',
      requiresAuth: false
    }
  }
];

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { top: 0 };
    }
  }
});

// Navigation guards
router.beforeEach(async (to, from, next) => {
  const toast = useToast();
  
  // Set page title
  if (to.meta.title) {
    document.title = to.meta.title;
  }

  // Check authentication
  const isAuthenticated = checkAuthentication();
  const userRole = getUserRole();

  // Redirect authenticated users away from login/register pages
  if (to.meta.hideForAuth && isAuthenticated) {
    next('/dashboard');
    return;
  }

  // Check if route requires authentication
  if (to.meta.requiresAuth && !isAuthenticated) {
    toast.warning('Please log in to access this page');
    next('/login');
    return;
  }

  // Check role-based access
  if (to.meta.roles && isAuthenticated) {
    if (!to.meta.roles.includes(userRole)) {
      toast.error('You do not have permission to access this page');
      next('/dashboard');
      return;
    }
  }

  next();
});

router.afterEach((to, from) => {
  // Analytics tracking could go here
  if (import.meta.env.PROD) {
    // Track page view
    console.log(`Navigated to: ${to.path}`);
  }
});

// Helper functions
function checkAuthentication() {
  // This should integrate with your auth store
  const token = localStorage.getItem('authToken');
  return !!token;
}

function getUserRole() {
  // This should integrate with your auth store
  const userStr = localStorage.getItem('user');
  if (userStr) {
    try {
      const user = JSON.parse(userStr);
      return user.role || 'client';
    } catch (error) {
      console.error('Error parsing user data:', error);
    }
  }
  return 'client';
}

export default router;
