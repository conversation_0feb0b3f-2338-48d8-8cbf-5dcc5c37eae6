import { ethers } from 'ethers';
import { useToast } from 'vue-toastification';
import { useProfitSharingStore } from '@/stores/profitSharing';

class BlockchainEventListener {
  constructor() {
    this.provider = null;
    this.contracts = {};
    this.isListening = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 10;
    this.reconnectDelay = 5000;
    this.toast = useToast();
    this.store = null;
  }

  async initialize() {
    try {
      // Initialize provider
      const rpcUrl = import.meta.env.VITE_ETH_RPC_URL || 'http://localhost:8545';
      const wsUrl = import.meta.env.VITE_ETH_WS_URL;

      if (wsUrl && (wsUrl.startsWith('ws://') || wsUrl.startsWith('wss://'))) {
        this.provider = new ethers.WebSocketProvider(wsUrl);
      } else {
        this.provider = new ethers.JsonRpcProvider(rpcUrl);
      }

      // Test connection
      const network = await this.provider.getNetwork();
      console.log(`Connected to blockchain network: ${network.name} (${network.chainId})`);

      // Initialize store
      this.store = useProfitSharingStore();

      // Load contracts
      await this.loadContracts();

      // Set up error handling
      this.setupErrorHandling();

      return true;
    } catch (error) {
      console.error('Failed to initialize blockchain event listener:', error);
      this.scheduleReconnect();
      return false;
    }
  }

  async loadContracts() {
    const contractAddresses = {
      profitSplitter: import.meta.env.VITE_PROFIT_SPLITTER_ADDRESS,
      capitalRecoveryTracker: import.meta.env.VITE_CAPITAL_RECOVERY_TRACKER_ADDRESS,
      tokenDistributor: import.meta.env.VITE_TOKEN_DISTRIBUTOR_ADDRESS,
      profitToken: import.meta.env.VITE_PROFIT_TOKEN_ADDRESS
    };

    // Load ProfitSplitter contract
    if (contractAddresses.profitSplitter) {
      const profitSplitterABI = [
        "event ProfitReceived(uint256 amount, address indexed sender)",
        "event ProfitDistributed(uint256 indexed distributionId, uint256 totalAmount, uint256 toCapitalPool, uint256 toRetainedShare, uint256 toTokenisedPool, uint8 stage)"
      ];
      
      this.contracts.profitSplitter = new ethers.Contract(
        contractAddresses.profitSplitter,
        profitSplitterABI,
        this.provider
      );
    }

    // Load CapitalRecoveryTracker contract
    if (contractAddresses.capitalRecoveryTracker) {
      const capitalRecoveryABI = [
        "event PaymentRecorded(uint256 amount, uint256 cumulativeTotal, address indexed recorder)",
        "event StageTransition(uint8 previousStage, uint8 newStage, uint256 timestamp)"
      ];
      
      this.contracts.capitalRecoveryTracker = new ethers.Contract(
        contractAddresses.capitalRecoveryTracker,
        capitalRecoveryABI,
        this.provider
      );
    }

    // Load TokenDistributor contract
    if (contractAddresses.tokenDistributor) {
      const tokenDistributorABI = [
        "event FundsReceived(uint256 amount, address indexed sender)",
        "event DistributionExecuted(uint256 indexed distributionId, uint256 totalAmount, uint256 timestamp)",
        "event PayoutSent(address indexed investor, uint256 amount, uint256 indexed distributionId)"
      ];
      
      this.contracts.tokenDistributor = new ethers.Contract(
        contractAddresses.tokenDistributor,
        tokenDistributorABI,
        this.provider
      );
    }

    // Load ProfitToken contract
    if (contractAddresses.profitToken) {
      const profitTokenABI = [
        "event KYCStatusUpdated(address indexed investor, bool verified)",
        "event TokensVested(address indexed investor, uint256 amount, uint256 vestingStart, uint256 vestingDuration)"
      ];
      
      this.contracts.profitToken = new ethers.Contract(
        contractAddresses.profitToken,
        profitTokenABI,
        this.provider
      );
    }

    console.log('Blockchain contracts loaded successfully');
  }

  setupErrorHandling() {
    if (this.provider.websocket) {
      this.provider.websocket.on('error', (error) => {
        console.error('WebSocket error:', error);
        this.scheduleReconnect();
      });

      this.provider.websocket.on('close', () => {
        console.warn('WebSocket connection closed');
        this.scheduleReconnect();
      });
    }

    this.provider.on('error', (error) => {
      console.error('Provider error:', error);
      this.scheduleReconnect();
    });
  }

  startListening() {
    if (this.isListening) {
      console.warn('Event listener is already running');
      return;
    }

    this.setupEventListeners();
    this.isListening = true;
    this.reconnectAttempts = 0;
    console.log('Blockchain event listener started');
  }

  setupEventListeners() {
    // Listen for StageTransition events
    if (this.contracts.capitalRecoveryTracker) {
      this.contracts.capitalRecoveryTracker.on('StageTransition', (previousStage, newStage, timestamp) => {
        this.handleStageTransition(previousStage, newStage, timestamp);
      });

      this.contracts.capitalRecoveryTracker.on('PaymentRecorded', (amount, cumulativeTotal, recorder) => {
        this.handlePaymentRecorded(amount, cumulativeTotal, recorder);
      });
    }

    // Listen for Payout events
    if (this.contracts.tokenDistributor) {
      this.contracts.tokenDistributor.on('PayoutSent', (investor, amount, distributionId) => {
        this.handlePayoutSent(investor, amount, distributionId);
      });

      this.contracts.tokenDistributor.on('DistributionExecuted', (distributionId, totalAmount, timestamp) => {
        this.handleDistributionExecuted(distributionId, totalAmount, timestamp);
      });
    }

    // Listen for ProfitDistributed events
    if (this.contracts.profitSplitter) {
      this.contracts.profitSplitter.on('ProfitDistributed', (distributionId, totalAmount, toCapitalPool, toRetainedShare, toTokenisedPool, stage) => {
        this.handleProfitDistributed(distributionId, totalAmount, toCapitalPool, toRetainedShare, toTokenisedPool, stage);
      });
    }

    // Listen for KYC events
    if (this.contracts.profitToken) {
      this.contracts.profitToken.on('KYCStatusUpdated', (investor, verified) => {
        this.handleKYCStatusUpdated(investor, verified);
      });
    }
  }

  handleStageTransition(previousStage, newStage, timestamp) {
    const stageNames = ['Capital Recovery', 'Post-Recovery'];
    const prevStageName = stageNames[previousStage] || 'Unknown';
    const newStageName = stageNames[newStage] || 'Unknown';
    
    console.log(`Stage transition: ${prevStageName} -> ${newStageName}`);
    
    this.toast.success(`Stage transition: ${prevStageName} → ${newStageName}`, {
      timeout: 5000
    });

    // Refresh dashboard data
    if (this.store) {
      this.store.refreshCapitalStatus();
      this.store.refreshDashboardData();
    }
  }

  handlePaymentRecorded(amount, cumulativeTotal, recorder) {
    const amountEth = ethers.formatEther(amount);
    const cumulativeEth = ethers.formatEther(cumulativeTotal);
    
    console.log(`Payment recorded: ${amountEth} ETH, cumulative: ${cumulativeEth} ETH`);
    
    this.toast.info(`Payment recorded: ${amountEth} ETH`, {
      timeout: 3000
    });

    // Refresh capital status
    if (this.store) {
      this.store.refreshCapitalStatus();
    }
  }

  handlePayoutSent(investor, amount, distributionId) {
    const amountEth = ethers.formatEther(amount);
    
    console.log(`Payout sent: ${amountEth} ETH to ${investor}`);
    
    this.toast.success(`Payout sent: ${amountEth} ETH`, {
      timeout: 3000
    });

    // Refresh investor data if it's the current user
    if (this.store) {
      this.store.refreshInvestorData();
    }
  }

  handleDistributionExecuted(distributionId, totalAmount, timestamp) {
    const totalEth = ethers.formatEther(totalAmount);
    
    console.log(`Distribution executed: ${totalEth} ETH`);
    
    this.toast.success(`Distribution executed: ${totalEth} ETH`, {
      timeout: 3000
    });

    // Refresh dashboard data
    if (this.store) {
      this.store.refreshDashboardData();
    }
  }

  handleProfitDistributed(distributionId, totalAmount, toCapitalPool, toRetainedShare, toTokenisedPool, stage) {
    const totalEth = ethers.formatEther(totalAmount);
    const stageName = stage === 0 ? 'Capital Recovery' : 'Post-Recovery';
    
    console.log(`Profit distributed: ${totalEth} ETH in ${stageName} stage`);
    
    this.toast.success(`Profit distributed: ${totalEth} ETH`, {
      timeout: 5000
    });

    // Refresh all dashboard data
    if (this.store) {
      this.store.refreshCapitalStatus();
      this.store.refreshDashboardData();
    }
  }

  handleKYCStatusUpdated(investor, verified) {
    console.log(`KYC status updated: ${investor} -> ${verified}`);
    
    this.toast.info(`KYC status updated: ${verified ? 'Verified' : 'Unverified'}`, {
      timeout: 3000
    });

    // Refresh investor data
    if (this.store) {
      this.store.refreshInvestorData();
    }
  }

  scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached. Stopping event listener.');
      this.toast.error('Blockchain connection lost. Please refresh the page.', {
        timeout: 0
      });
      return;
    }

    this.isListening = false;
    this.reconnectAttempts++;
    
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    
    console.log(`Scheduling reconnection attempt ${this.reconnectAttempts} in ${delay}ms`);
    
    setTimeout(async () => {
      try {
        await this.initialize();
        this.startListening();
        this.toast.success('Blockchain connection restored', {
          timeout: 3000
        });
      } catch (error) {
        console.error('Reconnection failed:', error);
        this.scheduleReconnect();
      }
    }, delay);
  }

  stopListening() {
    this.isListening = false;
    
    // Remove all listeners
    Object.values(this.contracts).forEach(contract => {
      if (contract) {
        contract.removeAllListeners();
      }
    });

    // Close WebSocket connection if exists
    if (this.provider.websocket) {
      this.provider.websocket.close();
    }

    console.log('Blockchain event listener stopped');
  }

  getConnectionStatus() {
    return {
      isListening: this.isListening,
      reconnectAttempts: this.reconnectAttempts,
      maxReconnectAttempts: this.maxReconnectAttempts,
      hasContracts: Object.keys(this.contracts).length > 0
    };
  }
}

// Create singleton instance
export const eventListener = new BlockchainEventListener();

// Auto-initialize when imported
if (typeof window !== 'undefined') {
  eventListener.initialize().then((success) => {
    if (success) {
      eventListener.startListening();
    }
  });
}

export default eventListener;
