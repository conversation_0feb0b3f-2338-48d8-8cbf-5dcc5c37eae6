<template>
  <div class="investor-dashboard">
    <div class="container-fluid">
      <!-- Header -->
      <div class="row mb-4">
        <div class="col">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h1 class="h3 mb-1">Investor Dashboard</h1>
              <p class="text-muted mb-0">Track your profit sharing and token holdings</p>
            </div>
            <div class="d-flex gap-2">
              <button 
                class="btn btn-outline-primary btn-sm"
                @click="refreshData"
                :disabled="loading.investorDashboard"
              >
                <i class="bi bi-arrow-clockwise me-1"></i>
                Refresh
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Capital Recovery Progress -->
      <div class="row mb-4">
        <div class="col">
          <CapitalProgress
            :target-amount="capitalStatus.target_amount"
            :repaid-amount="capitalStatus.repaid_amount"
            :remaining-amount="capitalStatus.remaining_amount"
            :stage="capitalStatus.stage"
            :loading="loading.capitalStatus"
            :show-timeline="true"
          />
        </div>
      </div>

      <!-- KPI Cards -->
      <div class="row mb-4">
        <div class="col-md-3 mb-3">
          <KpiCard
            title="Current Stage"
            :value="formattedCapitalStatus.stage_display"
            icon="bi bi-flag-fill"
            format="text"
            :variant="capitalStatus.stage === 'post_recovery' ? 'success' : 'warning'"
            :loading="loading.capitalStatus"
          >
            <template #footer>
              <small class="text-muted">
                {{ capitalRecoveryProgress.toFixed(1) }}% complete
              </small>
            </template>
          </KpiCard>
        </div>
        
        <div class="col-md-3 mb-3">
          <KpiCard
            title="Token Balance"
            :value="totalTokenBalance"
            icon="bi bi-coin"
            format="number"
            variant="info"
            :loading="loading.investorDashboard"
          >
            <template #footer>
              <small class="text-muted">APT Tokens</small>
            </template>
          </KpiCard>
        </div>
        
        <div class="col-md-3 mb-3">
          <KpiCard
            title="Total Received"
            :value="investorDashboard.total_received"
            icon="bi bi-cash-stack"
            format="currency"
            variant="success"
            :loading="loading.investorDashboard"
          >
            <template #footer>
              <small class="text-muted">All-time payouts</small>
            </template>
          </KpiCard>
        </div>
        
        <div class="col-md-3 mb-3">
          <KpiCard
            title="Recent Payout"
            :value="latestPayoutAmount"
            icon="bi bi-arrow-down-circle"
            format="currency"
            variant="primary"
            :loading="loading.investorDashboard"
          >
            <template #footer>
              <small class="text-muted">{{ latestPayoutDate }}</small>
            </template>
          </KpiCard>
        </div>
      </div>

      <div class="row">
        <!-- Token Holdings -->
        <div class="col-lg-6 mb-4">
          <div class="card h-100">
            <div class="card-header">
              <h5 class="card-title mb-0">Token Holdings</h5>
            </div>
            <div class="card-body">
              <div v-if="loading.investorDashboard" class="text-center py-4">
                <div class="spinner-border" role="status">
                  <span class="visually-hidden">Loading...</span>
                </div>
              </div>
              <div v-else-if="investorDashboard.token_balances.length === 0" class="text-center py-4 text-muted">
                <i class="bi bi-coin fs-1 d-block mb-2"></i>
                <p>No token holdings found</p>
              </div>
              <div v-else>
                <div 
                  v-for="token in investorDashboard.token_balances" 
                  :key="token.id"
                  class="token-holding mb-3"
                >
                  <div class="d-flex justify-content-between align-items-start">
                    <div>
                      <h6 class="mb-1">{{ token.token_name }} ({{ token.token_symbol }})</h6>
                      <p class="text-muted mb-1 small">{{ token.contract_address }}</p>
                    </div>
                    <div class="text-end">
                      <div class="fw-bold">{{ formatTokenAmount(token.balance) }}</div>
                      <small class="text-muted">Balance</small>
                    </div>
                  </div>
                  <div class="row mt-2">
                    <div class="col-6">
                      <small class="text-muted">Vested:</small>
                      <div class="fw-semibold">{{ formatTokenAmount(token.vested_amount) }}</div>
                    </div>
                    <div class="col-6">
                      <small class="text-muted">KYC Status:</small>
                      <div>
                        <span :class="token.kyc_verified ? 'badge bg-success' : 'badge bg-warning'">
                          {{ token.kyc_verified ? 'Verified' : 'Pending' }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- KYC Panel -->
        <div class="col-lg-6 mb-4">
          <KycPanel
            :verified="isKYCVerified"
            :status="kycStatus"
            :investor-id="currentUser?.id"
            :token-id="1"
            :loading="loading.kycInitiation"
            @verification-initiated="handleKYCInitiated"
            @status-checked="handleKYCStatusChecked"
          />
        </div>
      </div>

      <!-- Payout History -->
      <div class="row">
        <div class="col">
          <DataTable
            title="Payout History"
            subtitle="Your profit distribution history"
            :data="investorDashboard.recent_payouts"
            :columns="payoutColumns"
            :loading="loading.investorDashboard"
            :searchable="true"
            search-placeholder="Search payouts..."
            :paginated="true"
            :page-size="10"
            empty-message="No payouts received yet"
          >
            <template #cell-amount="{ value }">
              <span class="fw-bold text-success">{{ formatCurrency(value) }}</span>
            </template>
            
            <template #cell-blockchain_tx_hash="{ value }">
              <TxHashLink 
                :hash="value" 
                :network="blockchainNetwork"
                :testnet="isTestnet"
              />
            </template>
            
            <template #cell-status="{ value }">
              <span :class="getStatusBadgeClass(value)">
                {{ value }}
              </span>
            </template>
            
            <template #cell-payout_date="{ value }">
              {{ formatDateTime(value) }}
            </template>
          </DataTable>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useToast } from 'vue-toastification';
import { useProfitSharingStore } from '@/stores/profitSharing';
// import { useAuthStore } from '@/stores/auth'; // Assuming you have an auth store

import KpiCard from '@/components/profit-sharing/KpiCard.vue';
import CapitalProgress from '@/components/profit-sharing/CapitalProgress.vue';
import DataTable from '@/components/profit-sharing/DataTable.vue';
import KycPanel from '@/components/profit-sharing/KycPanel.vue';
import TxHashLink from '@/components/profit-sharing/TxHashLink.vue';

const router = useRouter();
const toast = useToast();
const profitSharingStore = useProfitSharingStore();
// const authStore = useAuthStore();

// Reactive data
const refreshing = ref(false);

// Computed properties
const currentUser = computed(() => ({ id: 1, name: 'Test User' })); // TODO: Replace with actual auth store
const capitalStatus = computed(() => profitSharingStore.capitalStatus);
const investorDashboard = computed(() => profitSharingStore.investorDashboard);
const loading = computed(() => profitSharingStore.loading);
const formattedCapitalStatus = computed(() => profitSharingStore.formattedCapitalStatus);
const capitalRecoveryProgress = computed(() => profitSharingStore.capitalRecoveryProgress);
const totalTokenBalance = computed(() => profitSharingStore.totalTokenBalance);

const isKYCVerified = computed(() => {
  return investorDashboard.value.token_balances.some(token => token.kyc_verified);
});

const kycStatus = computed(() => {
  if (isKYCVerified.value) return 'verified';
  // You might want to add more sophisticated status detection here
  return 'unverified';
});

const latestPayoutAmount = computed(() => {
  const payouts = investorDashboard.value.recent_payouts;
  return payouts.length > 0 ? payouts[0].amount : 0;
});

const latestPayoutDate = computed(() => {
  const payouts = investorDashboard.value.recent_payouts;
  if (payouts.length === 0) return 'No payouts yet';
  return formatDate(payouts[0].payout_date);
});

const blockchainNetwork = computed(() => {
  return import.meta.env.VITE_BLOCKCHAIN_NETWORK || 'ethereum';
});

const isTestnet = computed(() => {
  return import.meta.env.VITE_IS_TESTNET === 'true';
});

// Table columns
const payoutColumns = [
  {
    key: 'payout_date',
    label: 'Date',
    sortable: true,
    width: '150px'
  },
  {
    key: 'amount',
    label: 'Amount',
    sortable: true,
    align: 'end',
    width: '120px'
  },
  {
    key: 'distribution_type',
    label: 'Type',
    sortable: true,
    width: '120px'
  },
  {
    key: 'blockchain_tx_hash',
    label: 'Transaction',
    width: '200px'
  },
  {
    key: 'status',
    label: 'Status',
    sortable: true,
    width: '100px'
  }
];

// Methods
async function refreshData() {
  if (!currentUser.value?.id) return;
  
  try {
    refreshing.value = true;
    await Promise.all([
      profitSharingStore.fetchInvestorDashboard(currentUser.value.id),
      profitSharingStore.fetchCapitalStatus()
    ]);
    toast.success('Dashboard data refreshed');
  } catch (error) {
    console.error('Failed to refresh dashboard:', error);
    toast.error('Failed to refresh dashboard data');
  } finally {
    refreshing.value = false;
  }
}

function handleKYCInitiated(result) {
  console.log('KYC verification initiated:', result);
}

function handleKYCStatusChecked() {
  console.log('KYC status checked');
}

function formatCurrency(amount) {
  return profitSharingStore.formatCurrency(amount);
}

function formatTokenAmount(amount) {
  return profitSharingStore.formatTokenAmount(amount);
}

function formatDate(date) {
  if (!date) return '';
  return new Date(date).toLocaleDateString('en-CA');
}

function formatDateTime(date) {
  if (!date) return '';
  return new Date(date).toLocaleString('en-CA');
}

function getStatusBadgeClass(status) {
  const classes = {
    completed: 'badge bg-success',
    pending: 'badge bg-warning',
    failed: 'badge bg-danger',
    processing: 'badge bg-info'
  };
  return classes[status] || 'badge bg-secondary';
}

// Lifecycle
onMounted(async () => {
  if (!currentUser.value) {
    router.push('/login');
    return;
  }
  
  await refreshData();
});
</script>

<style scoped>
.investor-dashboard {
  padding: 2rem 0;
}

.token-holding {
  padding: 1rem;
  border: 1px solid var(--bs-border-color);
  border-radius: 0.5rem;
  background: var(--bs-gray-50);
}

.token-holding:last-child {
  margin-bottom: 0 !important;
}

/* Dark mode adjustments */
[data-bs-theme="dark"] .token-holding {
  background: var(--bs-gray-800);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .investor-dashboard {
    padding: 1rem 0;
  }
}
</style>
