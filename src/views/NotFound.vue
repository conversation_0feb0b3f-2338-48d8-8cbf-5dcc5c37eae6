<template>
  <div class="not-found">
    <div class="container">
      <div class="row justify-content-center">
        <div class="col-md-6 text-center">
          <div class="not-found__content">
            <i class="bi bi-exclamation-triangle-fill text-warning display-1 mb-4"></i>
            <h1 class="display-4 mb-3">404</h1>
            <h2 class="h4 mb-3">Page Not Found</h2>
            <p class="text-muted mb-4">
              The page you're looking for doesn't exist or has been moved.
            </p>
            <div class="d-flex gap-2 justify-content-center">
              <router-link to="/" class="btn btn-primary">
                <i class="bi bi-house me-1"></i>
                Go Home
              </router-link>
              <button class="btn btn-outline-secondary" @click="goBack">
                <i class="bi bi-arrow-left me-1"></i>
                Go Back
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';

const router = useRouter();

function goBack() {
  if (window.history.length > 1) {
    router.go(-1);
  } else {
    router.push('/');
  }
}
</script>

<style scoped>
.not-found {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 0;
}

.not-found__content {
  padding: 2rem;
}
</style>
