<template>
  <div class="admin-dashboard">
    <div class="container-fluid">
      <!-- Header -->
      <div class="row mb-4">
        <div class="col">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h1 class="h3 mb-1">Admin Dashboard</h1>
              <p class="text-muted mb-0">Manage profit sharing and investor verification</p>
            </div>
            <div class="d-flex gap-2">
              <button 
                class="btn btn-outline-primary btn-sm"
                @click="refreshData"
                :disabled="loading.adminDashboard"
              >
                <i class="bi bi-arrow-clockwise me-1"></i>
                Refresh
              </button>
              <button 
                class="btn btn-primary btn-sm"
                @click="showProfitSubmissionModal = true"
              >
                <i class="bi bi-plus-circle me-1"></i>
                Submit Profit
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Capital Pool Summary -->
      <div class="row mb-4">
        <div class="col">
          <CapitalProgress
            :target-amount="capitalStatus.target_amount"
            :repaid-amount="capitalStatus.repaid_amount"
            :remaining-amount="capitalStatus.remaining_amount"
            :stage="capitalStatus.stage"
            :loading="loading.capitalStatus"
            :show-timeline="false"
          />
        </div>
      </div>

      <!-- Statistics Cards -->
      <div class="row mb-4">
        <div class="col-md-3 mb-3">
          <KpiCard
            title="Verified Investors"
            :value="adminDashboard.statistics.verified_investors"
            icon="bi bi-shield-check"
            format="number"
            variant="success"
            :loading="loading.adminDashboard"
          />
        </div>
        
        <div class="col-md-3 mb-3">
          <KpiCard
            title="Pending KYC"
            :value="adminDashboard.statistics.pending_kyc"
            icon="bi bi-clock"
            format="number"
            variant="warning"
            :loading="loading.adminDashboard"
          />
        </div>
        
        <div class="col-md-3 mb-3">
          <KpiCard
            title="Total Distributed"
            :value="adminDashboard.statistics.total_distributed"
            icon="bi bi-cash-stack"
            format="currency"
            variant="info"
            :loading="loading.adminDashboard"
          />
        </div>
        
        <div class="col-md-3 mb-3">
          <KpiCard
            title="Total Profits"
            :value="adminDashboard.statistics.total_profits"
            icon="bi bi-graph-up"
            format="currency"
            variant="primary"
            :loading="loading.adminDashboard"
          />
        </div>
      </div>

      <div class="row">
        <!-- Recent Distributions -->
        <div class="col-lg-8 mb-4">
          <DataTable
            title="Recent Distributions"
            subtitle="Latest profit distributions and payouts"
            :data="adminDashboard.recent_distributions"
            :columns="distributionColumns"
            :loading="loading.adminDashboard"
            :searchable="true"
            search-placeholder="Search distributions..."
            :paginated="true"
            :page-size="8"
            empty-message="No distributions yet"
          >
            <template #cell-total_amount="{ value }">
              <span class="fw-bold">{{ formatCurrency(value) }}</span>
            </template>
            
            <template #cell-blockchain_tx_hash="{ value }">
              <TxHashLink 
                :hash="value" 
                :network="blockchainNetwork"
                :testnet="isTestnet"
              />
            </template>
            
            <template #cell-stage="{ value }">
              <span :class="value === 'capital_recovery' ? 'badge bg-warning' : 'badge bg-success'">
                {{ value === 'capital_recovery' ? 'Capital Recovery' : 'Post-Recovery' }}
              </span>
            </template>
            
            <template #cell-status="{ value }">
              <span :class="getStatusBadgeClass(value)">
                {{ value }}
              </span>
            </template>
          </DataTable>
        </div>

        <!-- Pending KYC -->
        <div class="col-lg-4 mb-4">
          <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
              <h5 class="card-title mb-0">Pending KYC Verifications</h5>
              <button class="btn btn-outline-primary btn-sm" @click="refreshKYCData">
                <i class="bi bi-arrow-clockwise"></i>
              </button>
            </div>
            <div class="card-body">
              <div v-if="loading.adminDashboard" class="text-center py-4">
                <div class="spinner-border" role="status">
                  <span class="visually-hidden">Loading...</span>
                </div>
              </div>
              <div v-else-if="pendingKYCInvestors.length === 0" class="text-center py-4 text-muted">
                <i class="bi bi-check-circle fs-1 d-block mb-2"></i>
                <p>All investors verified</p>
              </div>
              <div v-else class="pending-kyc-list">
                <div 
                  v-for="investor in pendingKYCInvestors" 
                  :key="investor.id"
                  class="pending-kyc-item"
                >
                  <div class="d-flex justify-content-between align-items-start">
                    <div>
                      <h6 class="mb-1">{{ investor.name || investor.email }}</h6>
                      <small class="text-muted">{{ investor.email }}</small>
                    </div>
                    <div class="d-flex gap-1">
                      <button 
                        class="btn btn-success btn-sm"
                        @click="approveKYC(investor.id)"
                        title="Approve KYC"
                      >
                        <i class="bi bi-check"></i>
                      </button>
                      <button 
                        class="btn btn-danger btn-sm"
                        @click="rejectKYC(investor.id)"
                        title="Reject KYC"
                      >
                        <i class="bi bi-x"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Recent Profit Submissions -->
      <div class="row">
        <div class="col">
          <DataTable
            title="Recent Profit Submissions"
            subtitle="Monthly profit submissions and processing status"
            :data="adminDashboard.recent_profit_submissions"
            :columns="profitSubmissionColumns"
            :loading="loading.adminDashboard"
            :searchable="true"
            search-placeholder="Search submissions..."
            :paginated="true"
            :page-size="10"
            empty-message="No profit submissions yet"
          >
            <template #cell-net_profit="{ value }">
              <span class="fw-bold">{{ formatCurrency(value) }}</span>
            </template>
            
            <template #cell-plains_north_share="{ value }">
              <span class="fw-bold text-primary">{{ formatCurrency(value) }}</span>
            </template>
            
            <template #cell-blockchain_tx_hash="{ value }">
              <TxHashLink 
                :hash="value" 
                :network="blockchainNetwork"
                :testnet="isTestnet"
              />
            </template>
            
            <template #cell-status="{ value }">
              <span :class="getStatusBadgeClass(value)">
                {{ value }}
              </span>
            </template>
          </DataTable>
        </div>
      </div>
    </div>

    <!-- Profit Submission Modal -->
    <div 
      class="modal fade" 
      :class="{ show: showProfitSubmissionModal }"
      :style="{ display: showProfitSubmissionModal ? 'block' : 'none' }"
      tabindex="-1"
    >
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Submit Monthly Profit</h5>
            <button 
              type="button" 
              class="btn-close" 
              @click="showProfitSubmissionModal = false"
            ></button>
          </div>
          <div class="modal-body">
            <form @submit.prevent="submitProfit">
              <div class="row mb-3">
                <div class="col-md-6">
                  <label class="form-label">Net Profit (CAD)</label>
                  <input 
                    type="number" 
                    class="form-control" 
                    v-model="profitForm.net_profit"
                    step="0.01"
                    required
                  />
                </div>
                <div class="col-md-6">
                  <label class="form-label">Plains North Share</label>
                  <input 
                    type="text" 
                    class="form-control" 
                    :value="formatCurrency(plainsNorthShare)"
                    readonly
                    disabled
                  />
                </div>
              </div>
              
              <div class="row mb-3">
                <div class="col-md-6">
                  <label class="form-label">Period Start</label>
                  <input 
                    type="date" 
                    class="form-control" 
                    v-model="profitForm.period_start"
                    required
                  />
                </div>
                <div class="col-md-6">
                  <label class="form-label">Period End</label>
                  <input 
                    type="date" 
                    class="form-control" 
                    v-model="profitForm.period_end"
                    required
                  />
                </div>
              </div>

              <!-- Distribution Preview -->
              <div class="alert alert-info">
                <h6>Distribution Preview</h6>
                <div v-if="isCapitalRecoveryStage">
                  <p class="mb-1"><strong>Current Stage:</strong> Capital Recovery</p>
                  <p class="mb-0">100% of Plains North's share ({{ formatCurrency(plainsNorthShare) }}) will go to capital pool recovery.</p>
                </div>
                <div v-else>
                  <p class="mb-1"><strong>Current Stage:</strong> Post-Recovery</p>
                  <ul class="mb-0">
                    <li>Retained Share: {{ formatCurrency(plainsNorthShare / 2) }}</li>
                    <li>Tokenised Pool: {{ formatCurrency(plainsNorthShare / 2) }}</li>
                  </ul>
                </div>
              </div>

              <div class="d-flex justify-content-end gap-2">
                <button 
                  type="button" 
                  class="btn btn-secondary"
                  @click="showProfitSubmissionModal = false"
                >
                  Cancel
                </button>
                <button 
                  type="submit" 
                  class="btn btn-primary"
                  :disabled="loading.profitSubmission"
                >
                  <span v-if="loading.profitSubmission" class="spinner-border spinner-border-sm me-2"></span>
                  Submit Profit
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
    <div 
      v-if="showProfitSubmissionModal" 
      class="modal-backdrop fade show"
      @click="showProfitSubmissionModal = false"
    ></div>
  </div>
</template>

<script setup>
import { computed, onMounted, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useToast } from 'vue-toastification';
import { useProfitSharingStore } from '@/stores/profitSharing';

import KpiCard from '@/components/profit-sharing/KpiCard.vue';
import CapitalProgress from '@/components/profit-sharing/CapitalProgress.vue';
import DataTable from '@/components/profit-sharing/DataTable.vue';
import TxHashLink from '@/components/profit-sharing/TxHashLink.vue';

const router = useRouter();
const toast = useToast();
const profitSharingStore = useProfitSharingStore();

// Reactive data
const showProfitSubmissionModal = ref(false);
const profitForm = ref({
  net_profit: '',
  period_start: '',
  period_end: ''
});

// Computed properties
const capitalStatus = computed(() => profitSharingStore.capitalStatus);
const adminDashboard = computed(() => profitSharingStore.adminDashboard);
const loading = computed(() => profitSharingStore.loading);
const isCapitalRecoveryStage = computed(() => profitSharingStore.isCapitalRecoveryStage);

const plainsNorthShare = computed(() => {
  return parseFloat(profitForm.value.net_profit || 0) / 3;
});

const pendingKYCInvestors = computed(() => {
  // This would come from your actual data
  return [
    { id: 1, name: 'John Doe', email: '<EMAIL>' },
    { id: 2, name: 'Jane Smith', email: '<EMAIL>' }
  ];
});

const blockchainNetwork = computed(() => {
  return import.meta.env.VITE_BLOCKCHAIN_NETWORK || 'ethereum';
});

const isTestnet = computed(() => {
  return import.meta.env.VITE_IS_TESTNET === 'true';
});

// Table columns
const distributionColumns = [
  { key: 'distribution_date', label: 'Date', sortable: true, width: '120px' },
  { key: 'distribution_type', label: 'Type', sortable: true, width: '120px' },
  { key: 'total_amount', label: 'Amount', sortable: true, align: 'end', width: '120px' },
  { key: 'stage', label: 'Stage', sortable: true, width: '120px' },
  { key: 'blockchain_tx_hash', label: 'Transaction', width: '180px' },
  { key: 'status', label: 'Status', sortable: true, width: '100px' }
];

const profitSubmissionColumns = [
  { key: 'submission_date', label: 'Date', sortable: true, width: '120px' },
  { key: 'submitted_by_name', label: 'Submitted By', sortable: true, width: '150px' },
  { key: 'net_profit', label: 'Net Profit', sortable: true, align: 'end', width: '120px' },
  { key: 'plains_north_share', label: 'PN Share', sortable: true, align: 'end', width: '120px' },
  { key: 'period_start', label: 'Period', sortable: true, width: '120px' },
  { key: 'blockchain_tx_hash', label: 'Transaction', width: '180px' },
  { key: 'status', label: 'Status', sortable: true, width: '100px' }
];

// Methods
async function refreshData() {
  try {
    await Promise.all([
      profitSharingStore.fetchAdminDashboard(),
      profitSharingStore.fetchCapitalStatus()
    ]);
    toast.success('Dashboard data refreshed');
  } catch (error) {
    console.error('Failed to refresh dashboard:', error);
    toast.error('Failed to refresh dashboard data');
  }
}

async function refreshKYCData() {
  // Implement KYC data refresh
  toast.info('KYC data refreshed');
}

async function submitProfit() {
  try {
    await profitSharingStore.submitMonthlyProfit(profitForm.value);
    showProfitSubmissionModal.value = false;
    resetProfitForm();
    await refreshData();
  } catch (error) {
    console.error('Failed to submit profit:', error);
  }
}

async function approveKYC(investorId) {
  try {
    await profitSharingStore.updateKYCStatus(investorId, 1, true);
    toast.success('KYC approved');
    await refreshData();
  } catch (error) {
    console.error('Failed to approve KYC:', error);
  }
}

async function rejectKYC(investorId) {
  try {
    await profitSharingStore.updateKYCStatus(investorId, 1, false);
    toast.success('KYC rejected');
    await refreshData();
  } catch (error) {
    console.error('Failed to reject KYC:', error);
  }
}

function resetProfitForm() {
  profitForm.value = {
    net_profit: '',
    period_start: '',
    period_end: ''
  };
}

function formatCurrency(amount) {
  return profitSharingStore.formatCurrency(amount);
}

function getStatusBadgeClass(status) {
  const classes = {
    completed: 'badge bg-success',
    pending: 'badge bg-warning',
    failed: 'badge bg-danger',
    processing: 'badge bg-info'
  };
  return classes[status] || 'badge bg-secondary';
}

// Set default period dates
watch(() => showProfitSubmissionModal.value, (show) => {
  if (show && !profitForm.value.period_start) {
    const now = new Date();
    const firstDay = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const lastDay = new Date(now.getFullYear(), now.getMonth(), 0);
    
    profitForm.value.period_start = firstDay.toISOString().split('T')[0];
    profitForm.value.period_end = lastDay.toISOString().split('T')[0];
  }
});

// Lifecycle
onMounted(async () => {
  await refreshData();
});
</script>

<style scoped>
.admin-dashboard {
  padding: 2rem 0;
}

.pending-kyc-item {
  padding: 1rem;
  border: 1px solid var(--bs-border-color);
  border-radius: 0.5rem;
  margin-bottom: 0.75rem;
  background: var(--bs-gray-50);
}

.pending-kyc-item:last-child {
  margin-bottom: 0;
}

.modal.show {
  background: rgba(0, 0, 0, 0.5);
}

/* Dark mode adjustments */
[data-bs-theme="dark"] .pending-kyc-item {
  background: var(--bs-gray-800);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .admin-dashboard {
    padding: 1rem 0;
  }
}
</style>
