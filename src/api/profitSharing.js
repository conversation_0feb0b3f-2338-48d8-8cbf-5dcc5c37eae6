import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000';

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: `${API_BASE_URL}/profit-sharing`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('authToken');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle response errors
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('authToken');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export const profitSharingApi = {
  // Dashboard endpoints
  async getInvestorDashboard(investorId) {
    try {
      const response = await apiClient.get(`/dashboard/investor/${investorId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching investor dashboard:', error);
      throw error;
    }
  },

  async getAdminDashboard() {
    try {
      const response = await apiClient.get('/dashboard/admin');
      return response.data;
    } catch (error) {
      console.error('Error fetching admin dashboard:', error);
      throw error;
    }
  },

  // Capital recovery endpoints
  async getCapitalStatus() {
    try {
      const response = await apiClient.get('/capital/status');
      return response.data;
    } catch (error) {
      console.error('Error fetching capital status:', error);
      throw error;
    }
  },

  async updateCapitalRecovery(amount, txHash) {
    try {
      const response = await apiClient.post('/capital/update', {
        amount,
        tx_hash: txHash
      });
      return response.data;
    } catch (error) {
      console.error('Error updating capital recovery:', error);
      throw error;
    }
  },

  // Profit submission endpoints
  async submitProfit(profitData) {
    try {
      const response = await apiClient.post('/accounting/profit', profitData);
      return response.data;
    } catch (error) {
      console.error('Error submitting profit:', error);
      throw error;
    }
  },

  // Token balance endpoints
  async getTokenBalance(investorId, tokenId = null) {
    try {
      const params = tokenId ? { token_id: tokenId } : {};
      const response = await apiClient.get(`/token/balance/${investorId}`, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching token balance:', error);
      throw error;
    }
  },

  // Payout history endpoints
  async getPayoutHistory(investorId, limit = 50, offset = 0) {
    try {
      const response = await apiClient.get(`/payout/history/${investorId}`, {
        params: { limit, offset }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching payout history:', error);
      throw error;
    }
  },

  // KYC endpoints
  async initiateKYC(investorId, tokenId) {
    try {
      const response = await apiClient.post('/kyc/initiate', {
        investor_id: investorId,
        token_id: tokenId
      });
      return response.data;
    } catch (error) {
      console.error('Error initiating KYC:', error);
      throw error;
    }
  },

  async updateKYCStatus(investorId, tokenId, verified, metadata = {}) {
    try {
      const response = await apiClient.post('/kyc/verify', {
        investor_id: investorId,
        token_id: tokenId,
        verified,
        metadata
      });
      return response.data;
    } catch (error) {
      console.error('Error updating KYC status:', error);
      throw error;
    }
  },

  // Distribution endpoints
  async createDistribution(distributionData) {
    try {
      const response = await apiClient.post('/distribution/create', distributionData);
      return response.data;
    } catch (error) {
      console.error('Error creating distribution:', error);
      throw error;
    }
  },

  // Event monitoring endpoints
  async getRecentEvents(limit = 50, eventType = null) {
    try {
      const params = { limit };
      if (eventType) params.event_type = eventType;
      
      const response = await apiClient.get('/events/recent', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching recent events:', error);
      throw error;
    }
  },

  async getEventStats() {
    try {
      const response = await apiClient.get('/events/stats');
      return response.data;
    } catch (error) {
      console.error('Error fetching event stats:', error);
      throw error;
    }
  },

  // Service management endpoints
  async getServiceHealth() {
    try {
      const response = await apiClient.get('/service/health');
      return response.data;
    } catch (error) {
      console.error('Error fetching service health:', error);
      throw error;
    }
  },

  async restartService() {
    try {
      const response = await apiClient.post('/service/restart');
      return response.data;
    } catch (error) {
      console.error('Error restarting service:', error);
      throw error;
    }
  },

  async reprocessEvents(fromBlock, toBlock) {
    try {
      const response = await apiClient.post('/events/reprocess', {
        from_block: fromBlock,
        to_block: toBlock
      });
      return response.data;
    } catch (error) {
      console.error('Error reprocessing events:', error);
      throw error;
    }
  },

  async getContractAddresses() {
    try {
      const response = await apiClient.get('/contracts/addresses');
      return response.data;
    } catch (error) {
      console.error('Error fetching contract addresses:', error);
      throw error;
    }
  }
};

// KYC provider integration
export const kycApi = {
  async initiateVerification(investorId) {
    try {
      // This would integrate with your chosen KYC provider
      // For now, we'll use a placeholder endpoint
      const response = await axios.post(`${API_BASE_URL}/kyc/initiate`, {
        investor_id: investorId,
        provider: 'sumsub' // or 'stripe', 'trulioo', etc.
      }, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('authToken')}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error initiating KYC verification:', error);
      throw error;
    }
  },

  async getVerificationStatus(investorId) {
    try {
      const response = await axios.get(`${API_BASE_URL}/kyc/status/${investorId}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('authToken')}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching KYC status:', error);
      throw error;
    }
  },

  async submitVerificationDocuments(investorId, documents) {
    try {
      const formData = new FormData();
      formData.append('investor_id', investorId);
      
      documents.forEach((doc, index) => {
        formData.append(`document_${index}`, doc.file);
        formData.append(`document_${index}_type`, doc.type);
      });

      const response = await axios.post(`${API_BASE_URL}/kyc/documents`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          Authorization: `Bearer ${localStorage.getItem('authToken')}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error submitting KYC documents:', error);
      throw error;
    }
  }
};

export default profitSharingApi;
