# Profit Sharing System Documentation

## Overview

The Profit Sharing System is a comprehensive solution for managing profit distributions to tokenized investors. It implements a two-stage capital recovery model where profits are first used to recover initial capital, then split between retained shares and tokenized profit pools.

## Architecture

### Backend Components

#### Database Schema
- **roles**: User role management (plains_north, operator, licence_holder, investor)
- **capital_pool**: Tracks capital recovery progress and stage transitions
- **tokens**: Manages profit-sharing tokens and metadata
- **token_holdings**: Tracks investor token balances and vesting
- **distributions**: Records profit distribution events
- **individual_payouts**: Individual investor payouts from distributions
- **profit_submissions**: Monthly profit submissions and approvals
- **blockchain_events**: Blockchain event tracking and processing

#### Services
- **CapitalRecoveryService**: Manages capital recovery tracking and stage transitions
- **ProfitSharingService**: Handles profit submissions, approvals, and distributions
- **BlockchainService**: Integrates with smart contracts and blockchain events
- **EventListenerService**: Listens to and processes blockchain events
- **KYCVerificationService**: Enhanced KYC with compliance checks

#### API Endpoints
```
/profit-sharing/
├── capital/
│   ├── status              # GET - Capital recovery status
│   ├── update              # POST - Update capital recovery
│   ├── history             # GET - Capital recovery history
│   └── statistics          # GET - Recovery statistics
├── accounting/
│   └── profit/
│       ├── /               # POST - Submit monthly profit
│       └── /:id/approve    # POST - Approve profit submission
├── token/
│   └── balance/:investor   # GET - Token balance for investor
├── payout/
│   └── history/:investor   # GET - Payout history for investor
├── distributions/          # GET - Distribution history
├── dashboard/
│   ├── investor/:id        # GET - Investor dashboard data
│   └── admin               # GET - Admin dashboard data
├── kyc/
│   └── verify              # POST - Update KYC status
├── blockchain/
│   ├── health              # GET - Blockchain service health
│   └── sync-tokens         # POST - Sync token holdings
├── events/
│   ├── restart             # POST - Restart event listener
│   └── recent              # GET - Recent blockchain events
└── statistics              # GET - Comprehensive statistics
```

### Frontend Components

#### Vue Components
- **KpiCard**: Reusable KPI display component with formatting and trends
- **CapitalProgress**: Capital recovery progress visualization with timeline
- **DataTable**: Advanced data table with sorting, pagination, and export
- **KycPanel**: KYC status display and management
- **TxHashLink**: Blockchain transaction hash display with explorer links
- **ProfitSubmissionModal**: Modal for submitting monthly profits

#### Views
- **InvestorDashboard**: Investor-facing dashboard with holdings and payouts
- **AdminDashboard**: Admin dashboard for profit management and system monitoring

#### Store (Pinia)
- **useProfitSharingStore**: Centralized state management for profit sharing data

## Business Logic

### Capital Recovery Model

The system implements a two-stage capital recovery model:

1. **Capital Recovery Stage** (Stage 1)
   - Target: CAD $150,000
   - 100% of Plains North's profit share (1/3 of net profit) goes to capital recovery
   - Progress tracked as percentage of target amount

2. **Post-Recovery Stage** (Stage 2)
   - Triggered when capital recovery target is reached
   - Plains North's profit share split 50/50:
     - 50% to retained share (Plains North)
     - 50% to tokenized profit pool (distributed to token holders)

### Profit Distribution Flow

1. **Monthly Profit Submission**
   - Operator submits gross profit, net profit, and period
   - System calculates Plains North's share (1/3 of net profit)
   - Submission enters "pending" status

2. **Approval Process**
   - Plains North admin reviews and approves submission
   - Approval triggers automatic distribution based on current stage

3. **Distribution Execution**
   - **Capital Recovery Stage**: 100% to capital pool
   - **Post-Recovery Stage**: 50% retained, 50% to token holders pro-rata

4. **Token Holder Payouts**
   - Only KYC-verified investors with token holdings receive payouts
   - Payouts calculated based on token balance percentage of total supply
   - Individual payout records created for audit trail

### KYC and Compliance

Enhanced KYC verification includes:
- Standard identity verification (face matching, document OCR)
- AML screening
- Sanctions checking
- PEP (Politically Exposed Person) verification
- Jurisdiction compliance
- Accredited investor verification

## Installation and Setup

### Backend Setup

1. **Database Migration**
   ```bash
   cd investor-app-backend
   node src/migrations/create_profit_sharing_tables.js
   ```

2. **Environment Variables**
   ```env
   # Blockchain Configuration
   ETH_RPC_URL=https://mainnet.infura.io/v3/YOUR_PROJECT_ID
   ETH_WS_URL=wss://mainnet.infura.io/v3/YOUR_PROJECT_ID
   BLOCKCHAIN_PRIVATE_KEY=your_private_key_here
   
   # Contract Addresses
   PROFIT_SPLITTER_CONTRACT_ADDRESS=0x...
   CAPITAL_RECOVERY_CONTRACT_ADDRESS=0x...
   TOKEN_DISTRIBUTOR_CONTRACT_ADDRESS=0x...
   PROFIT_TOKEN_CONTRACT_ADDRESS=0x...
   
   # Compliance Configuration
   ALLOWED_JURISDICTIONS=CA,US,GB,AU
   ```

3. **Start Services**
   ```bash
   npm start
   ```

### Frontend Setup

1. **Install Dependencies**
   ```bash
   cd investor-app-frontend
   npm install
   ```

2. **Configure API Endpoints**
   ```javascript
   // src/api/client.js
   const API_BASE_URL = process.env.VUE_APP_API_URL || 'http://localhost:3001';
   ```

3. **Start Development Server**
   ```bash
   npm run dev
   ```

## Testing

### Running Tests

Use the comprehensive test runner:

```bash
# Run all tests
node test-runner.js

# Run specific test suites
node test-runner.js --backend-only
node test-runner.js --frontend-only
node test-runner.js --e2e-only

# Run with coverage
node test-runner.js --coverage

# Watch mode for development
node test-runner.js --watch --unit-only
```

### Test Coverage

- **Backend Integration Tests**: API endpoints, business logic, database operations
- **Frontend Component Tests**: Vue component rendering, props, events, slots
- **Store Tests**: Pinia store actions, getters, state management
- **End-to-End Tests**: Complete user flows, cross-browser compatibility

## API Usage Examples

### Submit Monthly Profit

```javascript
const profitData = {
  gross_profit: 300000,
  net_profit: 150000,
  submission_period: '2024-01',
  description: 'January 2024 profit submission'
};

const response = await fetch('/profit-sharing/accounting/profit', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify(profitData)
});

const result = await response.json();
// result.data.plains_north_share = 50000
```

### Get Capital Status

```javascript
const response = await fetch('/profit-sharing/capital/status', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});

const capitalStatus = await response.json();
/*
{
  "success": true,
  "data": {
    "target_amount": 150000,
    "repaid_amount": 75000,
    "remaining_amount": 75000,
    "recovery_percentage": 50.0,
    "stage": "capital_recovery"
  }
}
*/
```

### Get Investor Dashboard

```javascript
const response = await fetch(`/profit-sharing/dashboard/investor/${investorId}`, {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});

const dashboard = await response.json();
/*
{
  "success": true,
  "data": {
    "capital_status": { ... },
    "token_balances": [ ... ],
    "recent_payouts": [ ... ],
    "total_received": 2500.00
  }
}
*/
```

## Security Considerations

### Authentication and Authorization
- JWT-based authentication required for all endpoints
- Role-based access control (RBAC) with specific permissions
- Admin-only operations protected with role verification

### Data Validation
- Input validation on all API endpoints
- SQL injection prevention with parameterized queries
- XSS protection with input sanitization

### Blockchain Security
- Private key management for contract interactions
- Transaction verification and confirmation tracking
- Event replay protection with unique transaction hashes

### Compliance
- KYC verification required for profit distributions
- AML screening integration
- Audit trail for all financial operations

## Monitoring and Maintenance

### System Health Monitoring
- Blockchain service connection status
- Event listener operational status
- Database connection health
- Contract interaction success rates

### Performance Metrics
- API response times
- Database query performance
- Token synchronization efficiency
- Distribution processing speed

### Logging and Auditing
- Comprehensive logging for all operations
- Audit trail for financial transactions
- Error tracking and alerting
- Performance monitoring

## Troubleshooting

### Common Issues

1. **Event Listener Not Processing Events**
   - Check blockchain connection
   - Verify contract addresses
   - Restart event listener service

2. **Token Balance Synchronization Issues**
   - Run manual token sync
   - Verify blockchain connectivity
   - Check contract ABI compatibility

3. **Distribution Calculation Errors**
   - Verify capital recovery stage
   - Check token supply calculations
   - Review KYC verification status

### Debug Commands

```bash
# Check system health
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:3001/profit-sharing/blockchain/health

# Restart event listener
curl -X POST -H "Authorization: Bearer $TOKEN" \
  http://localhost:3001/profit-sharing/events/restart

# Sync token holdings
curl -X POST -H "Authorization: Bearer $TOKEN" \
  http://localhost:3001/profit-sharing/blockchain/sync-tokens
```

## Future Enhancements

### Planned Features
- Multi-currency support
- Advanced reporting and analytics
- Automated compliance monitoring
- Integration with external accounting systems
- Mobile application support

### Scalability Improvements
- Database sharding for large datasets
- Caching layer for frequently accessed data
- Microservices architecture migration
- Real-time notification system

## Support and Maintenance

For technical support or questions about the profit sharing system:

1. Check the troubleshooting section above
2. Review system logs for error details
3. Run the test suite to verify system integrity
4. Contact the development team with specific error messages and reproduction steps

## License

This profit sharing system is proprietary software developed for Plains North Capital Inc. All rights reserved.
